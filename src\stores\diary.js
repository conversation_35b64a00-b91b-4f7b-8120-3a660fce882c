import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import dayjs from 'dayjs'

export const useDiaryStore = defineStore('diary', () => {
  // 日记列表
  const diaries = ref([])
  
  // 搜索关键词
  const searchKeyword = ref('')
  
  // 日期筛选
  const dateFilter = ref(null)
  
  // 生成唯一ID
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2)
  }
  
  // 添加日记
  const addDiary = (diary) => {
    const newDiary = {
      id: generateId(),
      content: diary.content,
      rating: diary.rating,
      date: diary.date || dayjs().format('YYYY-MM-DD'),
      createdAt: dayjs().toISOString(),
      updatedAt: dayjs().toISOString()
    }
    
    diaries.value.unshift(newDiary)
    saveDiaries()
    return newDiary
  }
  
  // 更新日记
  const updateDiary = (id, updates) => {
    const index = diaries.value.findIndex(d => d.id === id)
    if (index !== -1) {
      diaries.value[index] = {
        ...diaries.value[index],
        ...updates,
        updatedAt: dayjs().toISOString()
      }
      saveDiaries()
      return diaries.value[index]
    }
    return null
  }
  
  // 删除日记
  const deleteDiary = (id) => {
    const index = diaries.value.findIndex(d => d.id === id)
    if (index !== -1) {
      diaries.value.splice(index, 1)
      saveDiaries()
      return true
    }
    return false
  }
  
  // 根据ID获取日记
  const getDiaryById = (id) => {
    return diaries.value.find(d => d.id === id)
  }
  
  // 筛选后的日记列表
  const filteredDiaries = computed(() => {
    let filtered = diaries.value
    
    // 关键词搜索
    if (searchKeyword.value.trim()) {
      const keyword = searchKeyword.value.trim().toLowerCase()
      filtered = filtered.filter(diary => 
        diary.content.toLowerCase().includes(keyword)
      )
    }
    
    // 日期筛选
    if (dateFilter.value) {
      filtered = filtered.filter(diary => 
        diary.date === dateFilter.value
      )
    }
    
    return filtered.sort((a, b) => dayjs(b.date).valueOf() - dayjs(a.date).valueOf())
  })
  
  // 情绪统计
  const emotionStats = computed(() => {
    const stats = {
      1: 0, 2: 0, 3: 0, 4: 0, 5: 0
    }
    
    diaries.value.forEach(diary => {
      stats[diary.rating] = (stats[diary.rating] || 0) + 1
    })
    
    return stats
  })
  
  // 最近7天的情绪趋势
  const recentTrend = computed(() => {
    const last7Days = []
    for (let i = 6; i >= 0; i--) {
      const date = dayjs().subtract(i, 'day').format('YYYY-MM-DD')
      const dayDiaries = diaries.value.filter(d => d.date === date)
      const avgRating = dayDiaries.length > 0
        ? dayDiaries.reduce((sum, d) => sum + d.rating, 0) / dayDiaries.length
        : null // 改为 null 而不是 0，表示没有数据

      last7Days.push({
        date,
        rating: avgRating,
        count: dayDiaries.length,
        label: dayjs(date).format('MM/DD'),
        hasData: dayDiaries.length > 0 // 添加标识是否有数据
      })
    }
    
    return last7Days
  })
  
  // 平均心情评分
  const averageRating = computed(() => {
    if (diaries.value.length === 0) return 0
    const sum = diaries.value.reduce((acc, diary) => acc + diary.rating, 0)
    return (sum / diaries.value.length).toFixed(1)
  })
  
  // 保存到localStorage
  const saveDiaries = () => {
    localStorage.setItem('mood-diary-data', JSON.stringify(diaries.value))
  }
  
  // 从localStorage加载
  const loadDiaries = () => {
    const saved = localStorage.getItem('mood-diary-data')
    if (saved) {
      try {
        diaries.value = JSON.parse(saved)
      } catch (error) {
        console.error('加载日记数据失败:', error)
        diaries.value = []
      }
    } else {
      // 如果没有数据，创建一些示例数据
      createSampleData()
    }
  }
  
  // 创建示例数据
  const createSampleData = () => {
    const sampleDiaries = [
      {
        id: 'sample-1',
        content: '今天是个美好的一天！阳光明媚，心情特别好。去公园散步时看到了盛开的樱花，粉色的花瓣在微风中轻舞，让人心旷神怡。生活中的小确幸总是能带来大大的快乐。',
        rating: 5,
        date: dayjs().format('YYYY-MM-DD'),
        createdAt: dayjs().toISOString(),
        updatedAt: dayjs().toISOString()
      },
      {
        id: 'sample-2',
        content: '故地重游,好似刻舟求剑。',
        rating: 2,
        date: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
        createdAt: dayjs().subtract(1, 'day').toISOString(),
        updatedAt: dayjs().subtract(1, 'day').toISOString()
      },
      {
        id: 'sample-3',
        content: '心情很平静，没有特别开心也没有特别难过。今天读了一本书，学到了一些新的知识。傍晚时分，坐在窗边看着夕阳西下，感受着时间的流逝，内心很宁静。',
        rating: 3,
        date: dayjs().subtract(2, 'day').format('YYYY-MM-DD'),
        createdAt: dayjs().subtract(2, 'day').toISOString(),
        updatedAt: dayjs().subtract(2, 'day').toISOString()
      }
    ]
    
    diaries.value = sampleDiaries
    saveDiaries()
  }
  
  // 清空搜索和筛选
  const clearFilters = () => {
    searchKeyword.value = ''
    dateFilter.value = null
  }
  
  return {
    diaries,
    searchKeyword,
    dateFilter,
    filteredDiaries,
    emotionStats,
    recentTrend,
    averageRating,
    addDiary,
    updateDiary,
    deleteDiary,
    getDiaryById,
    loadDiaries,
    clearFilters
  }
}) 