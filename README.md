# 心情日记 🌸

一个充满情感与美感的心情记录网站，让每一份情绪都被温柔对待。

## ✨ 特性

### 🎨 设计理念
- **情感化设计**：界面简洁温柔，富有治愈气息
- **动态主题**：根据心情评分动态调整全局主色调
  - 🟠 开心时：温暖的橙黄色调
  - 🟢 平静时：柔和的灰绿色调  
  - 🔵 低落时：静谧的蓝灰或深紫色调
- **深浅色模式**：支持明暗主题切换

### 🎭 情绪表达
- **Lottie动画**：精致的情绪动画表情
  - 5星：跳跃欢呼的小人
  - 4星：微笑点头的卡通形象
  - 3星：闭眼呼吸的安宁状态
  - 2星：沉思低语的小动物
  - 1星：缓缓落雨或轻柔流泪的动画
- **星级评分**：直观的1-5星心情评分系统

### 📝 功能特性
- **日记管理**：添加、编辑、删除日记
- **智能搜索**：关键词搜索和日期筛选
- **数据持久化**：基于localStorage的本地存储
- **情绪统计**：Chart.js绘制的心情趋势图
- **响应式设计**：完美适配各种设备

## 🛠️ 技术栈

### 核心框架
- **Vue 3** - 现代化的前端框架
- **Vite** - 快速的构建工具
- **Pinia** - 状态管理
- **Vue Router** - 路由管理

### UI 组件库
- **Naive UI** - 美观易用的Vue3组件库
- **@vicons/ionicons5** - 精美的图标库

### 样式方案
- **SCSS** - 强大的CSS预处理器
- **CSS变量** - 动态主题系统
- **响应式设计** - 移动端友好

### 动画与图表
- **Chart.js** - 数据可视化

### 工具库
- **dayjs** - 轻量级日期处理

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 7.0.0

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 📁 项目结构

```
心情日记/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 组件
│   │   ├── Charts/        # 图表组件
│   │   ├── Layout/        # 布局组件
│   │   ├── DiaryCard.vue  # 日记卡片
│   │   └── MoodAnimation.vue # 心情动画
│   ├── stores/            # 状态管理
│   │   ├── diary.js       # 日记数据
│   │   └── theme.js       # 主题管理
│   ├── styles/            # 样式文件
│   │   ├── main.scss      # 主样式
│   │   └── variables.scss # 变量定义
│   ├── views/             # 页面组件
│   │   ├── Home.vue       # 首页
│   │   ├── AddDiary.vue   # 添加日记
│   │   ├── EditDiary.vue  # 编辑日记
│   │   └── Statistics.vue # 统计页面
│   ├── router/            # 路由配置
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── package.json
├── vite.config.js
└── README.md
```

## 🎯 核心功能

### 日记管理
- ✅ 创建新日记（日期、心情评分、内容）
- ✅ 编辑已有日记
- ✅ 删除日记（带确认）
- ✅ 按日期排序显示

### 搜索筛选
- ✅ 关键词全文搜索
- ✅ 按日期筛选
- ✅ 一键清空筛选条件

### 数据统计
- ✅ 近7天情绪趋势图
- ✅ 情绪分布统计
- ✅ 平均心情评分
- ✅ 记录天数统计

### 主题系统
- ✅ 明暗主题切换
- ✅ 根据心情动态调色
- ✅ 平滑过渡动画

## 🎨 设计亮点

### 情感化交互
- 卡片悬停效果
- 平滑的页面过渡
- 细腻的动画反馈
- 治愈系的色彩搭配

### 响应式设计
- 移动端优化
- 触摸友好的交互
- 自适应布局

### 无障碍支持
- 语义化HTML
- 键盘导航支持
- 合理的颜色对比度

## 🌟 特色体验

### 心情色彩
项目最大的特色是根据用户心情自动调整界面色调：
- 开心时界面呈现温暖的橙黄色
- 平静时转为清新的灰绿色
- 低落时显示静谧的蓝紫色

### 动画表情
每个心情评分都对应精心设计的Lottie动画，让情绪表达更加生动有趣。

### 数据可视化
通过Chart.js绘制的趋势图，让用户直观了解自己的情绪变化轨迹。

## 📱 兼容性

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+
- ✅ iOS Safari 14+
- ✅ Android Chrome 90+

## 🤝 贡献

欢迎提交Issue和Pull Request来帮助改进项目！

## 📄 开源协议

MIT License

---

**用心记录每一天，让情绪在代码中绽放** 🌸 