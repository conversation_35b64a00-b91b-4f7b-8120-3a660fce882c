<template>
  <footer class="app-footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-left">
          <p class="copyright">
            © 2025 心情日记 - 记录每一份美好时光
          </p>
        </div>
        
        <div class="footer-right">
          <div class="mood-indicator" v-if="diaryStore.diaries.length > 0">
            <span class="mood-text">今日心情：</span>
            <div class="mood-color-dot" :style="{ background: themeStore.currentTheme.primary }"></div>
            <span class="mood-name">{{ getMoodName() }}</span>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { computed } from 'vue'
import { useThemeStore } from '@/stores/theme'
import { useDiaryStore } from '@/stores/diary'
import dayjs from 'dayjs'

const themeStore = useThemeStore()
const diaryStore = useDiaryStore()

const todayDiaries = computed(() => {
  const today = dayjs().format('YYYY-MM-DD')
  return diaryStore.diaries.filter(d => d.date === today)
})

const getMoodName = () => {
  const moodNames = {
    happy: '开心',
    calm: '平静',
    sad: '低落',
    neutral: '一般'
  }
  return moodNames[themeStore.currentMoodColor] || '一般'
}
</script>

<style lang="scss" scoped>
.app-footer {
  background: var(--white);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-lg) 0;
  margin-top: auto;
  margin-bottom: 80px; // 为底部播放器预留空间，避免被遮挡
  transition: all var(--duration-300) var(--ease-out);

  @media (min-width: 769px) {
    margin-bottom: 100px; // PC端播放器更高，需要更多空间
  }
  
  .footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-md);
    
    @media (max-width: 640px) {
      flex-direction: column;
      text-align: center;
      gap: var(--spacing-sm);
    }
  }
  
  .footer-left {
    .copyright {
      color: var(--text-tertiary);
      font-size: var(--text-sm);
      transition: color var(--duration-300) var(--ease-out);
      
      .dark-theme & {
        color: var(--text-secondary);
      }
    }
  }
  
  .footer-right {
    .mood-indicator {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      
      .mood-text {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        transition: color var(--duration-300) var(--ease-out);
      }
      
      .mood-color-dot {
        width: 12px;
        height: 12px;
        border-radius: var(--radius-full);
        box-shadow: var(--shadow-sm);
        animation: pulse 2s infinite;
      }
      
      .mood-name {
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        color: var(--primary-color);
        transition: color var(--duration-300) var(--ease-out);
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}
</style> 