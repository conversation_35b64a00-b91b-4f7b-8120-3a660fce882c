<template>
  <footer class="app-footer" v-show="!musicStore.isPlaying && !musicStore.currentSong">
    <div class="container">
      <div class="footer-content">
        <div class="footer-left">
          <p class="copyright">
            © 2025 心情日记 - 记录每一份美好时光
          </p>
        </div>

        <div class="footer-right">
          <div class="mood-indicator" v-if="diaryStore.diaries.length > 0">
            <span class="mood-text">今日心情：</span>
            <div class="mood-color-dot" :style="{ background: themeStore.currentTheme.primary }"></div>
            <span class="mood-name">{{ getMoodName() }}</span>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { computed } from 'vue'
import { useThemeStore } from '@/stores/theme'
import { useDiaryStore } from '@/stores/diary'
import { useMusicStore } from '@/stores/music'
import dayjs from 'dayjs'

const themeStore = useThemeStore()
const diaryStore = useDiaryStore()
const musicStore = useMusicStore()

const todayDiaries = computed(() => {
  const today = dayjs().format('YYYY-MM-DD')
  return diaryStore.diaries.filter(d => d.date === today)
})

const getMoodName = () => {
  const moodNames = {
    happy: '开心',
    calm: '平静',
    sad: '低落',
    neutral: '一般'
  }
  return moodNames[themeStore.currentMoodColor] || '一般'
}
</script>

<style lang="scss" scoped>
.app-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--white);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-sm) 0;
  z-index: 999; // 确保在播放器下方但在其他内容上方
  transition: all var(--duration-300) var(--ease-out);
  backdrop-filter: blur(8px);

  @media (min-width: 769px) {
    padding: var(--spacing-md) 0;
  }
  
  .footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-md);

    @media (max-width: 640px) {
      flex-direction: row;
      justify-content: space-between;
      text-align: left;
      gap: var(--spacing-sm);
    }
  }
  
  .footer-left {
    .copyright {
      color: var(--text-tertiary);
      font-size: var(--text-xs);
      transition: color var(--duration-300) var(--ease-out);
      margin: 0;

      .dark-theme & {
        color: var(--text-secondary);
      }

      @media (min-width: 769px) {
        font-size: var(--text-sm);
      }
    }
  }
  
  .footer-right {
    .mood-indicator {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);

      .mood-text {
        font-size: var(--text-xs);
        color: var(--text-secondary);
        transition: color var(--duration-300) var(--ease-out);

        @media (min-width: 769px) {
          font-size: var(--text-sm);
        }
      }

      .mood-color-dot {
        width: 10px;
        height: 10px;
        border-radius: var(--radius-full);
        box-shadow: var(--shadow-sm);
        animation: pulse 2s infinite;

        @media (min-width: 769px) {
          width: 12px;
          height: 12px;
        }
      }

      .mood-name {
        font-size: var(--text-xs);
        font-weight: var(--font-medium);
        color: var(--primary-color);
        transition: color var(--duration-300) var(--ease-out);

        @media (min-width: 769px) {
          font-size: var(--text-sm);
        }
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}
</style> 