<template>
  <div class="music-player" v-if="musicStore.hasCurrentSong" :class="{ expanded: isExpanded }">
    <!-- 迷你播放器 -->
    <div class="mini-player" v-if="!isExpanded">
      <div class="player-content">
        <div class="song-info" @click="toggleExpanded">
          <div class="song-cover">
            <img :src="musicStore.currentSong.cover" :alt="musicStore.currentSong.name" />
            <div class="cover-animation" :class="{ playing: musicStore.isPlaying }"></div>
          </div>
          <div class="song-details">
            <div class="song-name">{{ musicStore.currentSong.name }}</div>
            <div class="song-artist">{{ musicStore.currentSong.artist }}</div>
          </div>
        </div>
        
        <div class="player-controls">
          <n-button
            circle
            type="primary"
            @click="togglePlay"
            :loading="musicStore.isLoading"
          >
            <template #icon>
              <n-icon>
                <PlayOutline v-if="!musicStore.isPlaying" />
                <PauseOutline v-else />
              </n-icon>
            </template>
          </n-button>
        </div>
        
        <div class="player-actions">
          <n-button
            circle
            quaternary
            @click="toggleFavorite"
            :type="musicStore.isFavorite(musicStore.currentSong.id) ? 'error' : 'default'"
          >
            <template #icon>
              <n-icon>
                <HeartOutline v-if="!musicStore.isFavorite(musicStore.currentSong.id)" />
                <Heart v-else />
              </n-icon>
            </template>
          </n-button>
          
          <n-button
            circle
            quaternary
            @click="toggleExpanded"
          >
            <template #icon>
              <n-icon><ChevronUpOutline /></n-icon>
            </template>
          </n-button>
        </div>
      </div>
      
      <!-- 进度条 -->
      <div class="progress-bar">
        <div 
          class="progress-fill" 
          :style="{ width: musicStore.progress + '%' }"
        ></div>
      </div>
    </div>
    
    <!-- 展开播放器 -->
    <div class="expanded-player" v-else>
      <div class="player-header">
        <h3>正在播放</h3>
        <n-button
          circle
          quaternary
          @click="toggleExpanded"
        >
          <template #icon>
            <n-icon><ChevronDownOutline /></n-icon>
          </template>
        </n-button>
      </div>
      
      <div class="player-main">
        <div class="song-artwork">
          <div class="artwork-container">
            <img :src="musicStore.currentSong.cover" :alt="musicStore.currentSong.name" />
            <div class="artwork-animation" :class="{ playing: musicStore.isPlaying }"></div>
          </div>
        </div>
        
        <div class="song-info-expanded">
          <h2 class="song-title">{{ musicStore.currentSong.name }}</h2>
          <p class="song-artist">{{ musicStore.currentSong.artist }}</p>
          <p class="song-album">{{ musicStore.currentSong.album }}</p>
        </div>
        
        <div class="progress-section">
          <div class="time-display">
            <span class="current-time">{{ musicStore.formattedCurrentTime }}</span>
            <span class="total-time">{{ musicStore.formattedDuration }}</span>
          </div>
          
          <n-slider
            v-model:value="progressValue"
            :step="0.1"
            :tooltip="false"
            @update:value="handleProgressChange"
            class="progress-slider"
          />
        </div>
        
        <!-- 歌词显示 -->
        <div class="lyrics-section" v-if="musicStore.lyrics.length > 0">
          <div class="lyrics-container" ref="lyricsContainer">
            <div class="lyrics-scroll">
              <!-- 顶部空白区域 -->
              <div class="lyrics-spacer"></div>

              <div
                v-for="(lyric, index) in musicStore.lyrics"
                :key="index"
                class="lyric-line"
                :class="{
                  active: index === musicStore.currentLyricIndex,
                  passed: index < musicStore.currentLyricIndex
                }"
                @click="seekToLyric(lyric.time)"
              >
                {{ lyric.text }}
              </div>

              <!-- 底部空白区域 -->
              <div class="lyrics-spacer"></div>
            </div>
          </div>
        </div>

        <!-- 歌词加载中 -->
        <div class="lyrics-loading" v-else-if="musicStore.lyricsLoading">
          <div class="loading-text">正在加载歌词...</div>
        </div>

        <!-- 无歌词提示 -->
        <div class="no-lyrics" v-else>
          <div class="no-lyrics-text">暂无歌词</div>
        </div>

        <!-- 歌词加载中 -->
        <div class="lyrics-loading" v-else>
          <n-spin size="small" />
          <span>加载歌词中...</span>
        </div>

        <div class="controls-section">
          <div class="main-controls">
            <n-button
              circle
              type="primary"
              size="large"
              @click="togglePlay"
              :loading="musicStore.isLoading"
              class="play-button"
            >
              <template #icon>
                <n-icon size="28">
                  <PlayOutline v-if="!musicStore.isPlaying" />
                  <PauseOutline v-else />
                </n-icon>
              </template>
            </n-button>
          </div>

          <div class="secondary-controls">
            <div class="volume-control">
              <n-button
                circle
                quaternary
                @click="toggleMute"
              >
                <template #icon>
                  <n-icon>
                    <VolumeHighOutline v-if="musicStore.volume > 0.5" />
                    <VolumeMediumOutline v-else-if="musicStore.volume > 0" />
                    <VolumeOffOutline v-else />
                  </n-icon>
                </template>
              </n-button>

              <n-slider
                v-model:value="volumeValue"
                :min="0"
                :max="100"
                :step="1"
                :tooltip="false"
                @update:value="handleVolumeChange"
                class="volume-slider"
              />
            </div>

            <n-button
              circle
              quaternary
              @click="toggleFavorite"
              :type="musicStore.isFavorite(musicStore.currentSong.id) ? 'error' : 'default'"
            >
              <template #icon>
                <n-icon>
                  <HeartOutline v-if="!musicStore.isFavorite(musicStore.currentSong.id)" />
                  <Heart v-else />
                </n-icon>
              </template>
            </n-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onUnmounted } from 'vue'
import { useMessage } from 'naive-ui'
import { useMusicStore } from '@/stores/music'
import {
  PlayOutline,
  PauseOutline,
  HeartOutline,
  Heart,
  ChevronUpOutline,
  ChevronDownOutline,
  VolumeHighOutline,
  VolumeMediumOutline,
  VolumeOffOutline,
  MusicalNotesOutline
} from '@vicons/ionicons5'

const message = useMessage()
const musicStore = useMusicStore()

// 播放器展开状态
const isExpanded = ref(false)

// 进度条值
const progressValue = ref(0)

// 音量值
const volumeValue = ref(80)

// 静音前的音量
const previousVolume = ref(0.8)

// 歌词容器引用
const lyricsContainer = ref(null)

// 监听播放进度
watch(() => musicStore.progress, (newProgress) => {
  progressValue.value = newProgress
})

// 监听音量变化
watch(() => musicStore.volume, (newVolume) => {
  volumeValue.value = newVolume * 100
})

// 监听当前歌词索引变化，自动滚动到当前歌词
watch(() => musicStore.currentLyricIndex, async (newIndex) => {
  if (!lyricsContainer.value || musicStore.lyrics.length === 0) return

  await nextTick()

  const lyricsScroll = lyricsContainer.value.querySelector('.lyrics-scroll')
  const lyricLines = lyricsContainer.value.querySelectorAll('.lyric-line')

  if (lyricLines[newIndex] && lyricsScroll) {
    const containerHeight = lyricsScroll.clientHeight
    const elementTop = lyricLines[newIndex].offsetTop
    const elementHeight = lyricLines[newIndex].clientHeight
    const scrollTop = elementTop - (containerHeight / 2) + (elementHeight / 2)

    lyricsScroll.scrollTo({
      top: scrollTop,
      behavior: 'smooth'
    })
  }
})

// 切换播放状态
const togglePlay = () => {
  if (musicStore.isPlaying) {
    musicStore.pause()
  } else {
    musicStore.resume()
  }
}

// 切换展开状态
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value

  // 防止背景滚动
  if (isExpanded.value) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = ''
  }
}

// 处理进度条变化
const handleProgressChange = (value) => {
  musicStore.setProgress(value)
}

// 处理音量变化
const handleVolumeChange = (value) => {
  musicStore.setVolume(value / 100)
}

// 切换静音
const toggleMute = () => {
  if (musicStore.volume > 0) {
    previousVolume.value = musicStore.volume
    musicStore.setVolume(0)
  } else {
    musicStore.setVolume(previousVolume.value)
  }
}



// 切换收藏状态
const toggleFavorite = () => {
  if (musicStore.isFavorite(musicStore.currentSong.id)) {
    musicStore.removeFromFavorites(musicStore.currentSong.id)
    message.success('已取消收藏')
  } else {
    musicStore.addToFavorites(musicStore.currentSong)
    message.success('已添加到收藏')
  }
}

// 跳转到指定歌词时间
const seekToLyric = (time) => {
  if (musicStore.audio && musicStore.duration > 0) {
    musicStore.setProgress((time / musicStore.duration) * 100)
  }
}

// 格式化歌词时间显示
const formatLyricTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 组件卸载时恢复滚动
onUnmounted(() => {
  document.body.style.overflow = ''
})
</script>

<style lang="scss" scoped>
.music-player {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--white);
  border-top: 1px solid var(--border-color);
  backdrop-filter: blur(12px);
  z-index: 1000;
  transition: all var(--duration-300) var(--ease-out);
  
  &.expanded {
    top: 0;
    background: rgba(var(--white), 0.95);
  }
  
  .mini-player {
    position: relative;
    
    .player-content {
      display: flex;
      align-items: center;
      padding: var(--spacing-md) var(--spacing-lg);
      gap: var(--spacing-md);
      
      .song-info {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        flex: 1;
        cursor: pointer;
        min-width: 0;
        
        .song-cover {
          position: relative;
          width: 50px;
          height: 50px;
          border-radius: var(--radius);
          overflow: hidden;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          
          .cover-animation {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border: 2px solid var(--primary-color);
            border-radius: var(--radius);
            opacity: 0;
            transform: scale(1.1);
            transition: all var(--duration-300) var(--ease-out);
            
            &.playing {
              opacity: 1;
              animation: pulse 2s infinite;
            }
          }
        }
        
        .song-details {
          min-width: 0;
          
          .song-name {
            font-weight: var(--font-medium);
            color: var(--text-primary);
            margin-bottom: 2px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          
          .song-artist {
            color: var(--text-secondary);
            font-size: var(--text-sm);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
      
      .player-controls {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
      }
      
      .player-actions {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
      }
    }
    
    .progress-bar {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: var(--gray-200);
      
      .progress-fill {
        height: 100%;
        background: var(--primary-color);
        transition: width var(--duration-200) var(--ease-out);
      }
    }
  }
  
  .expanded-player {
    height: 100vh;
    display: flex;
    flex-direction: column;
    
    .player-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-lg);
      border-bottom: 1px solid var(--border-color);
      
      h3 {
        color: var(--text-primary);
        font-size: var(--text-lg);
        font-weight: var(--font-semibold);
      }
    }
    
    .player-main {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: var(--spacing-2xl);
      gap: var(--spacing-xl);
      
      .song-artwork {
        .artwork-container {
          position: relative;
          width: 280px;
          height: 280px;
          border-radius: var(--radius-xl);
          overflow: hidden;
          box-shadow: var(--shadow-xl);
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          
          .artwork-animation {
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border: 3px solid var(--primary-color);
            border-radius: var(--radius-xl);
            opacity: 0;
            transform: scale(1.1);
            
            &.playing {
              opacity: 0.6;
              animation: rotate 10s linear infinite;
            }
          }
        }
      }
      
      .song-info-expanded {
        text-align: center;
        max-width: 400px;
        
        .song-title {
          font-size: var(--text-2xl);
          font-weight: var(--font-bold);
          color: var(--text-primary);
          margin-bottom: var(--spacing-sm);
        }
        
        .song-artist {
          font-size: var(--text-lg);
          color: var(--text-secondary);
          margin-bottom: var(--spacing-xs);
        }
        
        .song-album {
          font-size: var(--text-base);
          color: var(--text-tertiary);
        }
      }
      
      .progress-section {
        width: 100%;
        max-width: 400px;
        
        .time-display {
          display: flex;
          justify-content: space-between;
          margin-bottom: var(--spacing-sm);
          font-size: var(--text-sm);
          color: var(--text-tertiary);
        }
        
        .progress-slider {
          width: 100%;
        }
      }
      
      .lyrics-section {
        width: 100%;
        height: 400px;
        overflow: hidden;
        position: relative;
        background: transparent;

        .lyrics-container {
          width: 100%;
          height: 100%;
          position: relative;

          .lyrics-scroll {
            height: 100%;
            overflow-y: auto;
            padding: 0;

            &::-webkit-scrollbar {
              display: none;
            }

            .lyrics-spacer {
              height: 200px;
            }
          }

          .lyric-line {
            text-align: center;
            padding: 12px 20px;
            margin: 0;
            cursor: pointer;
            transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            font-size: 16px;
            line-height: 1.8;
            color: rgba(255, 255, 255, 0.6);
            font-weight: 400;
            letter-spacing: 0.5px;

            &:hover {
              color: rgba(255, 255, 255, 0.8);
            }

            &.active {
              color: #ffffff;
              font-size: 20px;
              font-weight: 600;
              text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
              transform: scale(1.05);
              background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(255, 255, 255, 0.05) 100%);
              border-radius: 12px;
              backdrop-filter: blur(10px);
              border: 1px solid rgba(255, 255, 255, 0.1);
            }

            &.passed {
              color: rgba(255, 255, 255, 0.3);
              font-size: 14px;
            }
          }
        }

        /* 渐变遮罩效果 */
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 100px;
          background: linear-gradient(to bottom,
            rgba(0, 0, 0, 0.8) 0%,
            transparent 100%);
          pointer-events: none;
          z-index: 1;
        }

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 100px;
          background: linear-gradient(to top,
            rgba(0, 0, 0, 0.8) 0%,
            transparent 100%);
          pointer-events: none;
          z-index: 1;
        }
      }

      .lyrics-loading {
        width: 100%;
        height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;

        .loading-text {
          color: rgba(255, 255, 255, 0.6);
          font-size: 16px;
          font-weight: 400;
        }
      }

      .no-lyrics {
        width: 100%;
        height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;

        .no-lyrics-text {
          color: rgba(255, 255, 255, 0.4);
          font-size: 18px;
          font-weight: 400;
          text-align: center;
        }
      }

      .controls-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-lg);

        .main-controls {
          display: flex;
          align-items: center;
          gap: var(--spacing-lg);

          .play-button {
            width: 64px;
            height: 64px;
          }
        }

        .secondary-controls {
          display: flex;
          align-items: center;
          gap: var(--spacing-lg);

          .volume-control {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);

            .volume-slider {
              width: 100px;
            }
          }
        }
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1.1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.15);
    opacity: 0.8;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg) scale(1.1);
  }
  to {
    transform: rotate(360deg) scale(1.1);
  }
}

@media (max-width: 768px) {
  .music-player {
    .mini-player {
      .player-content {
        padding: var(--spacing-sm) var(--spacing-md);
        gap: var(--spacing-sm);

        .song-info {
          .song-cover {
            width: 40px;
            height: 40px;
          }

          .song-details {
            .song-name {
              font-size: var(--text-sm);
            }

            .song-artist {
              font-size: var(--text-xs);
            }
          }
        }

        .player-controls {
          gap: var(--spacing-xs);

          .n-button {
            --n-height: 36px;
            --n-width: 36px;
          }
        }

        .player-actions {
          gap: var(--spacing-xs);

          .n-button {
            --n-height: 32px;
            --n-width: 32px;
          }
        }
      }
    }

    .expanded-player {
      .player-header {
        padding: var(--spacing-md);

        h3 {
          font-size: var(--text-base);
        }
      }

      .player-main {
        padding: var(--spacing-md);
        gap: var(--spacing-md);

        .song-artwork .artwork-container {
          width: 200px;
          height: 200px;
        }

        .song-info-expanded {
          .song-title {
            font-size: var(--text-xl);
          }

          .song-artist {
            font-size: var(--text-base);
          }

          .song-album {
            font-size: var(--text-sm);
          }
        }

        .lyrics-section {
          height: 150px;

          .lyrics-container {
            padding: var(--spacing-sm);

            .lyric-line {
              font-size: var(--text-xs);

              &.active {
                font-size: var(--text-sm);
              }
            }
          }
        }

        .controls-section {
          gap: var(--spacing-md);

          .main-controls {
            gap: var(--spacing-md);

            .play-button {
              width: 56px;
              height: 56px;
            }
          }

          .secondary-controls {
            flex-wrap: wrap;
            justify-content: center;
            gap: var(--spacing-sm);

            .volume-control {
              .volume-slider {
                width: 60px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
