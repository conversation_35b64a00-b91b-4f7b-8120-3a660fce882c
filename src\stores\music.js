import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useMusicStore = defineStore('music', () => {
  // 当前播放的歌曲
  const currentSong = ref(null)
  
  // 播放状态
  const isPlaying = ref(false)
  const isPaused = ref(false)
  const isLoading = ref(false)
  
  // 播放进度
  const currentTime = ref(0)
  const duration = ref(0)
  const volume = ref(0.8)
  
  // 播放模式 (0: 顺序播放, 1: 单曲循环, 2: 随机播放)
  const playMode = ref(0)
  
  // 播放列表
  const playlist = ref([])
  const currentIndex = ref(0)
  
  // 搜索相关
  const searchResults = ref([])
  const searchLoading = ref(false)
  const searchKeyword = ref('')
  const searchPage = ref(1)
  const searchHasMore = ref(true)
  const searchLoadingMore = ref(false)
  
  // 歌词
  const lyrics = ref([])
  const currentLyricIndex = ref(0)
  const lyricsLoading = ref(false)
  
  // 收藏列表
  const favorites = ref([])
  
  // 播放历史
  const playHistory = ref([])
  
  // Audio 实例
  const audio = ref(null)
  
  // 计算属性
  const progress = computed(() => {
    return duration.value > 0 ? (currentTime.value / duration.value) * 100 : 0
  })
  
  const formattedCurrentTime = computed(() => {
    return formatTime(currentTime.value)
  })
  
  const formattedDuration = computed(() => {
    return formatTime(duration.value)
  })
  
  const hasCurrentSong = computed(() => {
    return currentSong.value !== null
  })
  
  const canPlayPrevious = computed(() => {
    return playlist.value.length > 1 && currentIndex.value > 0
  })
  
  const canPlayNext = computed(() => {
    return playlist.value.length > 1 && currentIndex.value < playlist.value.length - 1
  })
  
  // 格式化时间
  const formatTime = (seconds) => {
    if (isNaN(seconds)) return '00:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  
  // 初始化音频
  const initAudio = () => {
    if (!audio.value) {
      audio.value = new Audio()
      audio.value.volume = volume.value
      
      // 监听音频事件
      audio.value.addEventListener('loadstart', () => {
        isLoading.value = true
      })
      
      audio.value.addEventListener('canplay', () => {
        isLoading.value = false
        duration.value = audio.value.duration || 0
      })
      
      audio.value.addEventListener('timeupdate', () => {
        currentTime.value = audio.value.currentTime || 0
        updateCurrentLyricIndex()
      })
      
      audio.value.addEventListener('ended', () => {
        handleSongEnd()
      })
      
      audio.value.addEventListener('error', async (e) => {
        console.error('音频播放错误:', e)
        isLoading.value = false
        isPlaying.value = false

        // 如果是网络错误，尝试重新获取播放链接
        if (currentSong.value && e.target.error && e.target.error.code === 4) {
          console.log('检测到网络错误，尝试重新获取播放链接...')
          const newUrl = await getMusicUrl(currentSong.value.id)
          if (newUrl && newUrl !== audio.value.src) {
            console.log('获取到新的播放链接，重新尝试播放')
            audio.value.src = newUrl
            audio.value.load()
            setTimeout(() => {
              audio.value.play().catch(err => {
                console.error('重新播放失败:', err)
              })
            }, 1000)
          }
        }
      })
    }
  }
  
  // 播放歌曲
  const playSong = async (song) => {
    try {
      initAudio()
      
      // 如果是同一首歌，只需要播放/暂停
      if (currentSong.value && currentSong.value.id === song.id) {
        if (isPlaying.value) {
          pause()
        } else {
          resume()
        }
        return
      }
      
      // 设置新歌曲
      currentSong.value = song
      isLoading.value = true
      
      // 获取播放链接
      const playUrl = await getMusicUrl(song.id)
      if (playUrl) {
        audio.value.src = playUrl
        audio.value.load()

        // 等待音频加载完成后播放
        const playHandler = async () => {
          try {
            await audio.value.play()
            isPlaying.value = true
            isPaused.value = false
            isLoading.value = false

            // 添加到播放历史
            addToHistory(song)

            // 获取歌词
            getLyrics(song.id)
          } catch (playError) {
            console.error('播放失败:', playError)
            isLoading.value = false
            isPlaying.value = false

            // 如果播放失败，可能是链接问题，尝试重新获取
            const retryUrl = await getMusicUrl(song.id)
            if (retryUrl && retryUrl !== playUrl) {
              audio.value.src = retryUrl
              audio.value.load()
              setTimeout(() => {
                audio.value.play().catch(err => {
                  console.error('重试播放失败:', err)
                })
              }, 1000)
            }
          }
        }

        audio.value.addEventListener('canplay', playHandler, { once: true })
      } else {
        isLoading.value = false
        console.error('无法获取播放链接')
      }
    } catch (error) {
      console.error('播放歌曲失败:', error)
      isLoading.value = false
    }
  }
  
  // 暂停播放
  const pause = () => {
    if (audio.value && !audio.value.paused) {
      audio.value.pause()
      isPlaying.value = false
      isPaused.value = true
    }
  }
  
  // 恢复播放
  const resume = () => {
    if (audio.value && audio.value.paused) {
      audio.value.play()
      isPlaying.value = true
      isPaused.value = false
    }
  }
  
  // 停止播放
  const stop = () => {
    if (audio.value) {
      audio.value.pause()
      audio.value.currentTime = 0
      isPlaying.value = false
      isPaused.value = false
      currentTime.value = 0
    }
  }
  
  // 设置播放进度
  const setProgress = (percent) => {
    if (audio.value && duration.value > 0) {
      const newTime = (percent / 100) * duration.value
      audio.value.currentTime = newTime
      currentTime.value = newTime
    }
  }
  
  // 设置音量
  const setVolume = (vol) => {
    volume.value = Math.max(0, Math.min(1, vol))
    if (audio.value) {
      audio.value.volume = volume.value
    }
  }
  
  // 上一首
  const playPrevious = () => {
    if (canPlayPrevious.value) {
      currentIndex.value--
      const prevSong = playlist.value[currentIndex.value]
      if (prevSong) {
        playSong(prevSong)
      }
    }
  }
  
  // 下一首
  const playNext = () => {
    if (canPlayNext.value) {
      currentIndex.value++
      const nextSong = playlist.value[currentIndex.value]
      if (nextSong) {
        playSong(nextSong)
      }
    } else if (playMode.value === 2) {
      // 随机播放
      playRandomSong()
    }
  }
  
  // 随机播放
  const playRandomSong = () => {
    if (playlist.value.length > 1) {
      let randomIndex
      do {
        randomIndex = Math.floor(Math.random() * playlist.value.length)
      } while (randomIndex === currentIndex.value)
      
      currentIndex.value = randomIndex
      const randomSong = playlist.value[randomIndex]
      if (randomSong) {
        playSong(randomSong)
      }
    }
  }
  
  // 处理歌曲结束
  const handleSongEnd = () => {
    if (playMode.value === 1) {
      // 单曲循环
      audio.value.currentTime = 0
      audio.value.play()
    } else {
      // 播放下一首
      playNext()
    }
  }
  
  // 切换播放模式
  const togglePlayMode = () => {
    playMode.value = (playMode.value + 1) % 3
  }
  
  // 添加到播放列表
  const addToPlaylist = (song) => {
    const exists = playlist.value.find(item => item.id === song.id)
    if (!exists) {
      playlist.value.push(song)
    }
  }
  
  // 从播放列表移除
  const removeFromPlaylist = (songId) => {
    const index = playlist.value.findIndex(item => item.id === songId)
    if (index > -1) {
      playlist.value.splice(index, 1)
      if (index <= currentIndex.value && currentIndex.value > 0) {
        currentIndex.value--
      }
    }
  }
  
  // 清空播放列表
  const clearPlaylist = () => {
    playlist.value = []
    currentIndex.value = 0
  }
  
  // 添加到收藏
  const addToFavorites = (song) => {
    const exists = favorites.value.find(item => item.id === song.id)
    if (!exists) {
      favorites.value.unshift(song)
      saveFavorites()
    }
  }
  
  // 从收藏移除
  const removeFromFavorites = (songId) => {
    const index = favorites.value.findIndex(item => item.id === songId)
    if (index > -1) {
      favorites.value.splice(index, 1)
      saveFavorites()
    }
  }
  
  // 检查是否已收藏
  const isFavorite = (songId) => {
    return favorites.value.some(item => item.id === songId)
  }
  
  // 添加到播放历史
  const addToHistory = (song) => {
    // 移除已存在的记录
    const existingIndex = playHistory.value.findIndex(item => item.id === song.id)
    if (existingIndex > -1) {
      playHistory.value.splice(existingIndex, 1)
    }
    
    // 添加到开头
    playHistory.value.unshift({
      ...song,
      playTime: new Date().toISOString()
    })
    
    // 限制历史记录数量
    if (playHistory.value.length > 100) {
      playHistory.value = playHistory.value.slice(0, 100)
    }
    
    saveHistory()
  }
  
  // 搜索音乐
  const searchMusic = async (keyword, page = 1, num = 10) => {
    const isFirstPage = page === 1

    if (isFirstPage) {
      searchLoading.value = true
      searchKeyword.value = keyword
      searchPage.value = 1
      searchResults.value = []
      searchHasMore.value = true
    } else {
      searchLoadingMore.value = true
    }

    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 10000) // 10秒超时

      const response = await fetch(`https://api.vkeys.cn/v2/music/netease?word=${encodeURIComponent(keyword)}&page=${page}&num=${num}`, {
        signal: controller.signal,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.code === 200) {
        // 如果返回的是数组，直接使用；如果是单个对象，转换为数组
        const results = Array.isArray(data.data) ? data.data : [data.data]
        const validResults = results.filter(Boolean)

        if (isFirstPage) {
          searchResults.value = validResults
        } else {
          searchResults.value = [...searchResults.value, ...validResults]
        }

        // 如果返回的结果少于请求的数量，说明没有更多了
        searchHasMore.value = validResults.length >= num
        searchPage.value = page

        return { success: true, data: validResults }
      } else {
        if (isFirstPage) {
          searchResults.value = []
        }
        return { success: false, message: data.message || '搜索失败' }
      }
    } catch (error) {
      console.error('搜索音乐失败:', error)
      if (isFirstPage) {
        searchResults.value = []
      }

      // 根据错误类型返回更具体的错误信息
      let errorMessage = '网络错误，请稍后重试'
      if (error.name === 'AbortError') {
        errorMessage = '请求超时，请稍后重试'
      } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
        errorMessage = '网络连接失败，请检查网络连接'
      } else if (error.message.includes('HTTP')) {
        errorMessage = '服务器响应异常，请稍后重试'
      }

      return { success: false, message: errorMessage }
    } finally {
      if (isFirstPage) {
        searchLoading.value = false
      } else {
        searchLoadingMore.value = false
      }
    }
  }

  // 加载更多搜索结果
  const loadMoreSearchResults = async () => {
    if (!searchKeyword.value || !searchHasMore.value || searchLoadingMore.value) {
      return
    }

    const nextPage = searchPage.value + 1
    return await searchMusic(searchKeyword.value, nextPage, 10)
  }

  // 获取歌词
  const getLyrics = async (songId) => {
    if (!songId) return

    lyricsLoading.value = true
    lyrics.value = [] // 清空之前的歌词
    currentLyricIndex.value = 0

    try {
      const response = await fetch(`https://api.vkeys.cn/v2/music/netease/lyric?id=${songId}`)
      const data = await response.json()

      if (data.code === 200 && data.data) {
        // 尝试获取歌词，优先使用 lrc，其次使用 lyric
        const lrcText = data.data.lrc || data.data.lyric

        if (lrcText) {
          const parsedLyrics = parseLyrics(lrcText)
          if (parsedLyrics.length > 0) {
            lyrics.value = parsedLyrics
            return { success: true, data: parsedLyrics }
          }
        }

        // 如果没有歌词内容，设置为空
        lyrics.value = []
        return { success: false, message: '暂无歌词' }
      } else {
        lyrics.value = []
        return { success: false, message: data.message || '获取歌词失败' }
      }
    } catch (error) {
      console.error('获取歌词失败:', error)
      lyrics.value = []
      return { success: false, message: '网络错误，请稍后重试' }
    } finally {
      lyricsLoading.value = false
    }
  }

  // 解析LRC歌词格式
  const parseLyrics = (lrcText) => {
    if (!lrcText) return []

    const lines = lrcText.split('\n')
    const lyrics = []

    lines.forEach(line => {
      // 支持多种时间格式：[mm:ss.xx] 或 [mm:ss]
      const match = line.match(/\[(\d{1,2}):(\d{2})(?:\.(\d{1,3}))?\](.*)/)
      if (match) {
        const minutes = parseInt(match[1])
        const seconds = parseInt(match[2])
        const milliseconds = match[3] ? parseInt(match[3].padEnd(3, '0')) : 0
        const text = match[4].trim()

        if (text && text !== '') {
          const time = minutes * 60 + seconds + milliseconds / 1000
          lyrics.push({
            time,
            text
          })
        }
      }
    })

    // 按时间排序并去重
    const uniqueLyrics = lyrics.filter((lyric, index, arr) => {
      return index === 0 || lyric.time !== arr[index - 1].time
    })

    return uniqueLyrics.sort((a, b) => a.time - b.time)
  }

  // 更新当前歌词索引
  const updateCurrentLyricIndex = () => {
    if (lyrics.value.length === 0) return

    const currentTime = audio.value?.currentTime || 0
    let index = 0

    for (let i = 0; i < lyrics.value.length; i++) {
      if (lyrics.value[i].time <= currentTime) {
        index = i
      } else {
        break
      }
    }

    currentLyricIndex.value = index
  }

  // 获取音乐播放链接（最高音质）
  const getMusicUrl = async (songId, quality = 9) => {
    try {
      const response = await fetch(`https://api.vkeys.cn/v2/music/netease?id=${songId}&quality=${quality}`)
      const data = await response.json()

      if (data.code === 200 && data.data && data.data.url) {
        return data.data.url
      }
      throw new Error(data.message || '获取播放链接失败')
    } catch (error) {
      console.error('获取音乐链接失败:', error)
      return null
    }
  }
  
  // 保存收藏到本地存储
  const saveFavorites = () => {
    localStorage.setItem('music-favorites', JSON.stringify(favorites.value))
  }
  
  // 加载收藏
  const loadFavorites = () => {
    const saved = localStorage.getItem('music-favorites')
    if (saved) {
      try {
        favorites.value = JSON.parse(saved)
      } catch (error) {
        console.error('加载收藏失败:', error)
      }
    }
  }
  
  // 保存播放历史
  const saveHistory = () => {
    localStorage.setItem('music-history', JSON.stringify(playHistory.value))
  }
  
  // 加载播放历史
  const loadHistory = () => {
    const saved = localStorage.getItem('music-history')
    if (saved) {
      try {
        playHistory.value = JSON.parse(saved)
      } catch (error) {
        console.error('加载播放历史失败:', error)
      }
    }
  }
  
  // 初始化
  const init = () => {
    loadFavorites()
    loadHistory()
  }
  
  return {
    // 状态
    currentSong,
    isPlaying,
    isPaused,
    isLoading,
    currentTime,
    duration,
    volume,
    playMode,
    playlist,
    currentIndex,
    searchResults,
    searchLoading,
    searchKeyword,
    searchPage,
    searchHasMore,
    searchLoadingMore,
    lyrics,
    currentLyricIndex,
    lyricsLoading,
    favorites,
    playHistory,
    
    // 计算属性
    progress,
    formattedCurrentTime,
    formattedDuration,
    hasCurrentSong,
    canPlayPrevious,
    canPlayNext,
    
    // 方法
    initAudio,
    playSong,
    pause,
    resume,
    stop,
    setProgress,
    setVolume,
    playPrevious,
    playNext,
    togglePlayMode,
    addToPlaylist,
    removeFromPlaylist,
    clearPlaylist,
    addToFavorites,
    removeFromFavorites,
    isFavorite,
    addToHistory,
    searchMusic,
    loadMoreSearchResults,
    getLyrics,
    getMusicUrl,
    init
  }
})
