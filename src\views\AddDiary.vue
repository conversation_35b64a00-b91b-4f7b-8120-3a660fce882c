<template>
  <div class="add-diary-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">        
        <div class="header-center">
          <h1 class="page-title">写日记</h1>
          <p class="page-subtitle">记录此刻的心情</p>
        </div>
        
        <div class="header-right">
          <MoodAnimation :rating="form.rating" :size="60" />
        </div>
      </div>
      
      <!-- 日记表单 -->
      <div class="diary-form">
        <n-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-placement="top"
          size="large"
        >
          <!-- 日期选择 -->
          <n-form-item label="日期" path="date">
            <n-date-picker
              v-model:value="form.dateValue"
              type="date"
              placeholder="选择日期"
              size="large"
              class="date-picker"
              @update:value="handleDateChange"
            />
          </n-form-item>
          
          <!-- 心情评分 -->
          <n-form-item label="心情评分" path="rating">
            <div class="rating-section">
              <div class="rating-input">
                <n-rate
                  v-model:value="form.rating"
                  size="large"
                  :color="themeStore.currentTheme.primary"
                  @update:value="handleRatingChange"
                />
                <span class="rating-text">{{ getRatingText(form.rating) }}</span>
              </div>
              <div class="rating-preview">
                <MoodAnimation :rating="form.rating" :size="80" />
              </div>
            </div>
          </n-form-item>
          
          <!-- 日记内容 -->
          <n-form-item label="今天发生了什么..." path="content">
            <n-input
              v-model:value="form.content"
              type="textarea"
              placeholder="在这里记录你的心情和感悟..."
              :rows="8"
              :maxlength="1000"
              show-count
              class="content-input"
            />
          </n-form-item>
          
          <!-- 操作按钮 -->
          <div class="form-actions">
            <n-button
              @click="$router.back()"
              size="large"
              class="cancel-btn"
            >
              取消
            </n-button>
            
            <n-button
              type="primary"
              size="large"
              @click="handleSubmit"
              :loading="loading"
              class="submit-btn"
            >
              <template #icon>
                <n-icon>
                  <SaveOutline />
                </n-icon>
              </template>
              保存日记
            </n-button>
          </div>
        </n-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { useDiaryStore } from '@/stores/diary'
import { useThemeStore } from '@/stores/theme'
import MoodAnimation from '@/components/MoodAnimation.vue'
import { getMoodText } from '@/constants/mood'
import dayjs from 'dayjs'
import { SaveOutline } from '@vicons/ionicons5'

const router = useRouter()
const message = useMessage()
const diaryStore = useDiaryStore()
const themeStore = useThemeStore()

const formRef = ref(null)
const loading = ref(false)

// 表单数据
const form = reactive({
  dateValue: Date.now(),
  date: dayjs().format('YYYY-MM-DD'),
  rating: 3,
  content: ''
})

// 表单验证规则
const rules = {
  rating: {
    required: true,
    type: 'number',
    min: 1,
    max: 5,
    message: '请选择心情评分',
    trigger: 'change'
  },
  content: {
    required: true,
    message: '请输入日记内容',
    trigger: 'input'
  }
}

// 处理日期变化
const handleDateChange = (value) => {
  if (value) {
    form.date = dayjs(value).format('YYYY-MM-DD')
  }
}

// 处理评分变化
const handleRatingChange = (rating) => {
  themeStore.setMoodByRating(rating)
}

// 获取评分文字（使用常量）
const getRatingText = (rating) => getMoodText(rating, true)

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    loading.value = true
    
    // 创建日记
    const newDiary = diaryStore.addDiary({
      content: form.content.trim(),
      rating: form.rating,
      date: form.date
    })
    
    message.success('日记保存成功！')
    
    // 延迟一下让用户看到成功消息
    setTimeout(() => {
      router.push('/')
    }, 1000)
    
  } catch (error) {
    message.error('请完善日记信息')
  } finally {
    loading.value = false
  }
}

// 监听评分变化，更新主题
watch(
  () => form.rating,
  (newRating) => {
    themeStore.setMoodByRating(newRating)
  },
  { immediate: true }
)

onMounted(() => {
  // 初始化主题
  themeStore.setMoodByRating(form.rating)
})
</script>

<style lang="scss" scoped>
.add-diary-page {
  min-height: 100vh;
  
  .page-header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin-bottom: var(--spacing-2xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-lg);
      text-align: center;
    }
    
    .header-center {
      .page-title {
        font-size: var(--text-3xl);
        font-weight: var(--font-bold);
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
        background: var(--gradient-bg);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
      
      .page-subtitle {
        color: var(--text-secondary);
        font-size: var(--text-lg);
      }
    }
    
    .header-right {
      position: absolute;
      right: 0;
      
      @media (max-width: 768px) {
        position: static;
        display: block;
      }
    }
  }
  
  .diary-form {
    max-width: 800px;
    margin: 0 auto;
    
    .date-picker {
      width: 200px;
    }
    
    .rating-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: var(--spacing-lg);
      
      @media (max-width: 640px) {
        flex-direction: column;
        gap: var(--spacing-md);
      }
      
      .rating-input {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
        
        :deep(.n-rate) {
          font-size: 24px;
        }
        
        .rating-text {
          font-size: var(--text-lg);
          font-weight: var(--font-medium);
          color: var(--primary-color);
        }
      }
      
      .rating-preview {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: var(--spacing-md);
        background: var(--gray-50);
        border-radius: var(--radius-lg);
        border: 2px dashed var(--border-color);
        transition: all var(--duration-300) var(--ease-out);
        
        &:hover {
          border-color: var(--primary-color);
          background: var(--primary-color-lighter);
        }
      }
    }
    
    .content-input {
      :deep(.n-input__textarea) {
        font-size: var(--text-base);
        line-height: var(--leading-relaxed);
        border-radius: var(--radius-lg);
        
        &:focus {
          border-color: var(--primary-color);
          box-shadow: 0 0 0 2px var(--primary-color)20;
        }
      }
    }
    
    .form-actions {
      display: flex;
      gap: var(--spacing-md);
      justify-content: flex-end;
      margin-top: var(--spacing-xl);
      
      @media (max-width: 640px) {
        flex-direction: column-reverse;
      }
      
      .cancel-btn {
        min-width: 120px;
      }
      
      .submit-btn {
        min-width: 140px;
        
        :deep(.n-button__content) {
          gap: var(--spacing-sm);
        }
      }
    }
  }
}

// 表单标签样式
:deep(.n-form-item-label) {
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  font-size: var(--text-base);
}

// 表单项间距
:deep(.n-form-item) {
  margin-bottom: var(--spacing-xl);
}
</style> 