// 音乐API相关函数

const BASE_URL = 'https://api.vkeys.cn/v2/music/netease'

/**
 * 搜索音乐
 * @param {string} keyword - 搜索关键词
 * @param {number} page - 页码，默认1
 * @param {number} num - 每页数量，默认10
 * @returns {Promise<Object>} 搜索结果
 */
export const searchMusic = async (keyword, page = 1, num = 10) => {
  try {
    const params = new URLSearchParams({
      word: keyword,
      page: page.toString(),
      num: num.toString()
    })
    
    const response = await fetch(`${BASE_URL}?${params}`)
    const data = await response.json()
    
    if (data.code === 200) {
      return {
        success: true,
        data: data.data,
        message: data.message
      }
    } else {
      return {
        success: false,
        data: null,
        message: data.message || '搜索失败'
      }
    }
  } catch (error) {
    console.error('搜索音乐失败:', error)
    return {
      success: false,
      data: null,
      message: '网络错误，请稍后重试'
    }
  }
}

/**
 * 根据ID获取音乐详情和播放链接
 * @param {number} id - 音乐ID
 * @param {number} quality - 音质等级，默认9（最高）
 * @returns {Promise<Object>} 音乐详情
 */
export const getMusicById = async (id, quality = 9) => {
  try {
    const params = new URLSearchParams({
      id: id.toString(),
      quality: quality.toString()
    })
    
    const response = await fetch(`${BASE_URL}?${params}`)
    const data = await response.json()
    
    if (data.code === 200) {
      return {
        success: true,
        data: data.data,
        message: data.message
      }
    } else {
      return {
        success: false,
        data: null,
        message: data.message || '获取音乐失败'
      }
    }
  } catch (error) {
    console.error('获取音乐失败:', error)
    return {
      success: false,
      data: null,
      message: '网络错误，请稍后重试'
    }
  }
}

/**
 * 获取歌词
 * @param {number} id - 音乐ID
 * @returns {Promise<Object>} 歌词数据
 */
export const getLyrics = async (id) => {
  try {
    const response = await fetch(`https://api.vkeys.cn/v2/music/netease/lyric?id=${id}`)
    const data = await response.json()
    
    if (data.code === 200) {
      return {
        success: true,
        data: data.data,
        message: data.message
      }
    } else {
      return {
        success: false,
        data: null,
        message: data.message || '获取歌词失败'
      }
    }
  } catch (error) {
    console.error('获取歌词失败:', error)
    return {
      success: false,
      data: null,
      message: '网络错误，请稍后重试'
    }
  }
}

/**
 * 解析LRC歌词格式
 * @param {string} lrcText - LRC格式歌词文本
 * @returns {Array} 解析后的歌词数组
 */
export const parseLyrics = (lrcText) => {
  if (!lrcText) return []
  
  const lines = lrcText.split('\n')
  const lyrics = []
  
  lines.forEach(line => {
    const match = line.match(/\[(\d{2}):(\d{2})\.(\d{2})\](.*)/)
    if (match) {
      const minutes = parseInt(match[1])
      const seconds = parseInt(match[2])
      const milliseconds = parseInt(match[3])
      const text = match[4].trim()
      
      if (text) {
        const time = minutes * 60 + seconds + milliseconds / 100
        lyrics.push({
          time,
          text
        })
      }
    }
  })
  
  return lyrics.sort((a, b) => a.time - b.time)
}

/**
 * 格式化时间（秒转为 mm:ss 格式）
 * @param {number|string} seconds - 秒数
 * @returns {string} 格式化后的时间字符串
 */
export const formatDuration = (seconds) => {
  if (!seconds || isNaN(seconds)) return '00:00'

  const totalSeconds = parseInt(seconds)
  const mins = Math.floor(totalSeconds / 60)
  const secs = totalSeconds % 60

  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

/**
 * 格式化音乐数据
 * @param {Object} rawData - 原始音乐数据
 * @returns {Object} 格式化后的音乐数据
 */
export const formatMusicData = (rawData) => {
  if (!rawData) return null

  return {
    id: rawData.id,
    name: rawData.song || rawData.name,
    artist: rawData.singer || rawData.artist,
    album: rawData.album,
    cover: rawData.cover || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAzMkMyNCAyNy41ODE3IDI3LjU4MTcgMjQgMzIgMjRDMzYuNDE4MyAyNCA0MCAyNy41ODE3IDQwIDMyQzQwIDM2LjQxODMgMzYuNDE4MyA0MCAzMiA0MEMyNy41ODE3IDQwIDI0IDM2LjQxODMgMjQgMzJaIiBmaWxsPSIjOUNBM0FGIi8+Cjwvc3ZnPgo=',
    duration: formatDuration(rawData.interval || rawData.time),
    url: rawData.url,
    quality: rawData.quality,
    size: rawData.size,
    kbps: rawData.kbps,
    link: rawData.link,
    time: rawData.time
  }
}

/**
 * 音质选项
 */
export const QUALITY_OPTIONS = [
  { value: 1, label: '标准（64k）' },
  { value: 2, label: '标准（128k）' },
  { value: 3, label: 'HQ极高（192k）' },
  { value: 4, label: 'HQ极高（320k）' },
  { value: 5, label: 'SQ无损' },
  { value: 6, label: '高解析度无损（Hi-Res）' },
  { value: 7, label: '高清臻音（Spatial Audio）' },
  { value: 8, label: '沉浸环绕声（Surround Audio）' },
  { value: 9, label: '超清母带（Master）' }
]

/**
 * 获取音质标签
 * @param {number} quality - 音质等级
 * @returns {string} 音质标签
 */
export const getQualityLabel = (quality) => {
  const option = QUALITY_OPTIONS.find(opt => opt.value === quality)
  return option ? option.label : '未知音质'
}
