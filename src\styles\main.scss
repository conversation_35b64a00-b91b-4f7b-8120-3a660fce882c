// 导入变量
@use './variables' as *;

// 全局重置
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--text-primary);
  line-height: var(--leading-normal);
  background: var(--white);
  overflow-x: hidden;
  transition: color var(--duration-300) var(--ease-out), background-color var(--duration-300) var(--ease-out);
}

// 通用样式类
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  
  @include respond-to(sm) {
    padding: 0 var(--spacing-xl);
  }
}

.page-container {
  min-height: 100vh;
  padding: var(--spacing-xl) 0;
  
  @include respond-to(md) {
    padding: var(--spacing-2xl) 0;
  }
}

.card {
  @include card-style;
  padding: var(--spacing-lg);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }
}

.btn {
  @include button-style;
  border: none;
  background: var(--primary-color);
  color: var(--white);
  text-decoration: none;
  
  &:hover {
    background: var(--secondary-color);
  }
  
  &.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    
    &:hover {
      background: var(--primary-color);
      color: var(--white);
    }
  }
  
  &.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    border: none;
    
    &:hover {
      background: var(--gray-100);
      color: var(--text-primary);
    }
  }
  
  &.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--text-sm);
  }
  
  &.btn-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--text-lg);
  }
}

// 文字样式
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-tertiary {
  color: var(--text-tertiary);
}

// 间距工具类
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

// 布局工具类
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-sm {
  gap: var(--spacing-sm);
}

.gap-md {
  gap: var(--spacing-md);
}

.gap-lg {
  gap: var(--spacing-lg);
}

// 网格布局
.grid {
  display: grid;
  gap: var(--spacing-lg);
  
  &.grid-1 {
    grid-template-columns: 1fr;
  }
  
  &.grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  &.grid-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  &.grid-responsive {
    grid-template-columns: 1fr;
    
    @include respond-to(md) {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @include respond-to(lg) {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--duration-300) var(--ease-out);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all var(--duration-300) var(--ease-out);
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: var(--radius-full);
  
  &:hover {
    background: var(--gray-400);
  }
}

// 深色模式滚动条
.dark-theme {
  ::-webkit-scrollbar-track {
    background: var(--gray-800);
  }
  
  ::-webkit-scrollbar-thumb {
    background: var(--gray-600);
    
    &:hover {
      background: var(--gray-500);
    }
  }
}

// 响应式隐藏
.hidden-sm {
  @include respond-to(sm) {
    display: none;
  }
}

.hidden-md {
  @include respond-to(md) {
    display: none;
  }
}

.show-sm {
  display: none;
  
  @include respond-to(sm) {
    display: block;
  }
}

.show-md {
  display: none;
  
  @include respond-to(md) {
    display: block;
  }
}

// 深色主题全局适配
.dark-theme {
  // 确保所有输入框在深色模式下都有正确的样式
  input, textarea, select {
    background-color: var(--gray-50);
    color: var(--text-primary);
    border-color: var(--border-color);
    
    &::placeholder {
      color: var(--text-tertiary);
    }
    
    &:focus {
      border-color: var(--primary-color);
      background-color: var(--gray-100);
    }
  }
  
  // 确保按钮在深色模式下的对比度
  button {
    &:not(.n-button--primary):not(.n-button--error) {
      color: var(--text-primary);
      
      &:hover {
        background-color: var(--gray-100);
      }
    }
  }
  
  // 优化卡片在深色模式下的显示
  .card, [class*="card"] {
    background-color: var(--white);
    border-color: var(--border-color);
    color: var(--text-primary);
  }
} 