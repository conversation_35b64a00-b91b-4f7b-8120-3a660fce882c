<template>
  <div class="emotion-trend-chart">
    <div class="chart-header">
      <h3 class="chart-title">近7天情绪趋势</h3>
      <div class="chart-legend">
        <div class="legend-item">
          <div class="legend-dot" :style="{ background: gradientColor }"></div>
          <span>心情评分</span>
        </div>
      </div>
    </div>
    
    <div class="chart-container">
      <canvas ref="chartCanvas" :width="canvasWidth" :height="canvasHeight"></canvas>
    </div>
    
    <div class="chart-footer">
      <div class="trend-summary">
        <div class="summary-item">
          <span class="label">平均评分</span>
          <span class="value">{{ averageRating }}★</span>
        </div>
        <div class="summary-item">
          <span class="label">最高评分</span>
          <span class="value">{{ maxRating }}★</span>
        </div>
        <div class="summary-item">
          <span class="label">最低评分</span>
          <span class="value">{{ minRating }}★</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { useThemeStore } from '@/stores/theme'
import { getMoodText } from '@/constants/mood'
import {
  Chart,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  LineController,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js'

// 注册Chart.js组件
Chart.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  LineController,
  Title,
  Tooltip,
  Legend,
  Filler
)

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  width: {
    type: Number,
    default: 400
  },
  height: {
    type: Number,
    default: 200
  }
})

const themeStore = useThemeStore()
const chartCanvas = ref(null)
let chartInstance = null

const canvasWidth = computed(() => props.width)
const canvasHeight = computed(() => props.height)

// 渐变色
const gradientColor = computed(() => themeStore.currentTheme.gradient)

// 统计数据
const averageRating = computed(() => {
  const validData = props.data.filter(item => item.rating > 0)
  if (validData.length === 0) return '0.0'
  const sum = validData.reduce((acc, item) => acc + item.rating, 0)
  return (sum / validData.length).toFixed(1)
})

const maxRating = computed(() => {
  const validData = props.data.filter(item => item.rating > 0)
  return validData.length > 0 ? Math.max(...validData.map(item => item.rating)).toFixed(1) : '0.0'
})

const minRating = computed(() => {
  const validData = props.data.filter(item => item.rating > 0)
  return validData.length > 0 ? Math.min(...validData.map(item => item.rating)).toFixed(1) : '0.0'
})

// 创建图表
const createChart = () => {
  if (!chartCanvas.value) return

  try {
    const ctx = chartCanvas.value.getContext('2d')
    
    // 创建渐变
    const gradient = ctx.createLinearGradient(0, 0, 0, canvasHeight.value)
    gradient.addColorStop(0, themeStore.currentTheme.primary + '40')
    gradient.addColorStop(1, themeStore.currentTheme.primary + '10')

    // 准备数据
    const labels = props.data.map(item => item.label)
    const ratings = props.data.map(item => item.rating || 0)

    chartInstance = new Chart(ctx, {
      type: 'line',
      data: {
        labels,
        datasets: [{
          label: '心情评分',
          data: ratings,
          borderColor: themeStore.currentTheme.primary,
          backgroundColor: gradient,
          borderWidth: 3,
          pointBackgroundColor: themeStore.currentTheme.primary,
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          pointRadius: 6,
          pointHoverRadius: 8,
          fill: true,
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
          intersect: false,
          mode: 'index'
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#ffffff',
            bodyColor: '#ffffff',
            borderColor: themeStore.currentTheme.primary,
            borderWidth: 1,
            cornerRadius: 8,
            displayColors: false,
            callbacks: {
              title: function(context) {
                return context[0].label
              },
              label: function(context) {
                const rating = context.parsed.y
                if (rating === null || rating === undefined) return '无记录'

                return `${rating.toFixed(1)}★ ${getMoodText(Math.round(rating))}`
              }
            }
          }
        },
        scales: {
          x: {
            grid: {
              display: false
            },
            ticks: {
              color: '#9ca3af'
            }
          },
          y: {
            min: 0,
            max: 5,
            ticks: {
              stepSize: 1,
              color: '#9ca3af',
              callback: function(value) {
                return value + '★'
              }
            },
            grid: {
              color: '#f3f4f6',
              borderDash: [2, 2]
            }
          }
        },
        elements: {
          point: {
            hoverBorderWidth: 3
          }
        },
        animation: {
          duration: 1000,
          easing: 'easeInOutQuart'
        }
      }
    })
  } catch (error) {
    console.error('创建图表时出错:', error)
  }
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return

  try {
    const ctx = chartCanvas.value.getContext('2d')
    const gradient = ctx.createLinearGradient(0, 0, 0, canvasHeight.value)
    gradient.addColorStop(0, themeStore.currentTheme.primary + '40')
    gradient.addColorStop(1, themeStore.currentTheme.primary + '10')

    chartInstance.data.labels = props.data.map(item => item.label)
    chartInstance.data.datasets[0].data = props.data.map(item => item.rating || 0)
    chartInstance.data.datasets[0].borderColor = themeStore.currentTheme.primary
    chartInstance.data.datasets[0].backgroundColor = gradient
    chartInstance.data.datasets[0].pointBackgroundColor = themeStore.currentTheme.primary
    chartInstance.options.plugins.tooltip.borderColor = themeStore.currentTheme.primary

    chartInstance.update('active')
  } catch (error) {
    console.error('更新图表时出错:', error)
  }
}

// 监听数据变化
watch(() => props.data, updateChart, { deep: true })

// 监听主题变化
watch(() => themeStore.currentTheme, updateChart, { deep: true })

onMounted(() => {
  createChart()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.destroy()
  }
})
</script>

<style lang="scss" scoped>
.emotion-trend-chart {
  @include card-style;
  padding: var(--spacing-lg);
  
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    
    .chart-title {
      font-size: var(--text-lg);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
    }
    
    .chart-legend {
      .legend-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: var(--text-sm);
        color: var(--text-secondary);
        
        .legend-dot {
          width: 12px;
          height: 12px;
          border-radius: var(--radius-full);
          box-shadow: var(--shadow-sm);
        }
      }
    }
  }
  
  .chart-container {
    position: relative;
    height: 300px;
    margin: var(--spacing-lg) 0;
    
    canvas {
      max-width: 100%;
      height: auto;
    }
  }
  
  .chart-footer {
    .trend-summary {
      display: flex;
      justify-content: space-around;
      gap: var(--spacing-md);
      padding-top: var(--spacing-lg);
      border-top: 1px solid var(--border-color);
      
      @media (max-width: 640px) {
        flex-direction: column;
        gap: var(--spacing-sm);
      }
      
      .summary-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-xs);
        
        @media (max-width: 640px) {
          flex-direction: row;
          justify-content: space-between;
        }
        
        .label {
          font-size: var(--text-sm);
          color: var(--text-tertiary);
        }
        
        .value {
          font-size: var(--text-lg);
          font-weight: var(--font-semibold);
          color: var(--primary-color);
        }
      }
    }
  }
}
</style> 