<template>
  <div class="edit-diary-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-left">
          <n-button 
            @click="$router.back()" 
            quaternary 
            size="large"
            class="back-btn"
          >
            <template #icon>
              <n-icon>
                <ArrowBackOutline />
              </n-icon>
            </template>
            返回
          </n-button>
        </div>
        
        <div class="header-center">
          <h1 class="page-title">编辑日记</h1>
          <p class="page-subtitle">修改心情记录</p>
        </div>
        
        <div class="header-right">
          <MoodAnimation :rating="form.rating" :size="60" v-if="form.rating" />
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="!diary" class="loading-state">
        <n-spin size="large">
          <div class="loading-content">
            <MoodAnimation :rating="3" :size="80" />
            <p>加载中...</p>
          </div>
        </n-spin>
      </div>
      
      <!-- 日记表单 -->
      <div v-else-if="diary" class="diary-form">
        <n-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-placement="top"
          size="large"
        >
          <!-- 日期显示 -->
          <n-form-item label="日期">
            <div class="date-display">
              <n-icon size="18">
                <CalendarOutline />
              </n-icon>
              <span>{{ formatDate(diary.date) }}</span>
            </div>
          </n-form-item>
          
          <!-- 心情评分 -->
          <n-form-item label="心情评分" path="rating">
            <div class="rating-section">
              <div class="rating-input">
                <n-rate
                  v-model:value="form.rating"
                  size="large"
                  :color="themeStore.currentTheme.primary"
                  @update:value="handleRatingChange"
                />
                <span class="rating-text">{{ getRatingText(form.rating) }}</span>
              </div>
              <div class="rating-preview">
                <MoodAnimation :rating="form.rating" :size="80" />
              </div>
            </div>
          </n-form-item>
          
          <!-- 日记内容 -->
          <n-form-item label="日记内容" path="content">
            <n-input
              v-model:value="form.content"
              type="textarea"
              placeholder="在这里记录你的心情和感悟..."
              :rows="8"
              :maxlength="1000"
              show-count
              class="content-input"
            />
          </n-form-item>
          
          <!-- 操作按钮 -->
          <div class="form-actions">
            <n-button
              @click="$router.back()"
              size="large"
              class="cancel-btn"
            >
              取消
            </n-button>
            
            <n-popconfirm
              @positive-click="handleDelete"
              positive-text="删除"
              negative-text="取消"
            >
              <template #trigger>
                <n-button
                  type="error"
                  size="large"
                  :loading="deleting"
                  class="delete-btn"
                >
                  <template #icon>
                    <n-icon>
                      <TrashOutline />
                    </n-icon>
                  </template>
                  删除
                </n-button>
              </template>
              确定要删除这条日记吗？
            </n-popconfirm>
            
            <n-button
              type="primary"
              size="large"
              @click="handleSubmit"
              :loading="loading"
              class="submit-btn"
            >
              <template #icon>
                <n-icon>
                  <SaveOutline />
                </n-icon>
              </template>
              保存更改
            </n-button>
          </div>
        </n-form>
      </div>
      
      <!-- 错误状态 -->
      <div v-else class="error-state">
        <div class="error-content">
          <MoodAnimation :rating="2" :size="120" />
          <h3>日记不存在</h3>
          <p>抱歉，找不到这条日记记录</p>
          <n-button type="primary" @click="$router.push('/')">
            返回首页
          </n-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMessage } from 'naive-ui'
import { useDiaryStore } from '@/stores/diary'
import { useThemeStore } from '@/stores/theme'
import MoodAnimation from '@/components/MoodAnimation.vue'
import { getMoodText } from '@/constants/mood'
import dayjs from 'dayjs'
import { 
  ArrowBackOutline, 
  SaveOutline, 
  TrashOutline, 
  CalendarOutline 
} from '@vicons/ionicons5'

const router = useRouter()
const route = useRoute()
const message = useMessage()
const diaryStore = useDiaryStore()
const themeStore = useThemeStore()

const formRef = ref(null)
const loading = ref(false)
const deleting = ref(false)

// 获取日记ID
const diaryId = route.params.id

// 获取日记数据
const diary = computed(() => diaryStore.getDiaryById(diaryId))

// 表单数据
const form = reactive({
  rating: 3,
  content: ''
})

// 表单验证规则
const rules = {
  rating: {
    required: true,
    type: 'number',
    min: 1,
    max: 5,
    message: '请选择心情评分',
    trigger: 'change'
  },
  content: {
    required: true,
    message: '请输入日记内容',
    trigger: 'input'
  }
}

// 初始化表单数据
const initForm = () => {
  if (diary.value) {
    form.rating = diary.value.rating
    form.content = diary.value.content
    themeStore.setMoodByRating(diary.value.rating)
  }
}

// 格式化日期
const formatDate = (date) => {
  const today = dayjs()
  const diaryDate = dayjs(date)
  
  if (diaryDate.isSame(today, 'day')) {
    return '今天 ' + diaryDate.format('YYYY年MM月DD日')
  } else if (diaryDate.isSame(today.subtract(1, 'day'), 'day')) {
    return '昨天 ' + diaryDate.format('YYYY年MM月DD日')
  } else {
    return diaryDate.format('YYYY年MM月DD日 dddd')
  }
}

// 处理评分变化
const handleRatingChange = (rating) => {
  themeStore.setMoodByRating(rating)
}

// 获取评分文字（使用常量）
const getRatingText = (rating) => getMoodText(rating, true)

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    loading.value = true
    
    // 更新日记
    const updated = diaryStore.updateDiary(diaryId, {
      content: form.content.trim(),
      rating: form.rating
    })
    
    if (updated) {
      message.success('日记更新成功！')
      setTimeout(() => {
        router.push('/')
      }, 1000)
    } else {
      message.error('更新失败')
    }
    
  } catch (error) {
    message.error('请完善日记信息')
  } finally {
    loading.value = false
  }
}

// 删除日记
const handleDelete = async () => {
  deleting.value = true
  
  try {
    const success = diaryStore.deleteDiary(diaryId)
    if (success) {
      message.success('日记已删除')
      setTimeout(() => {
        router.push('/')
      }, 1000)
    } else {
      message.error('删除失败')
    }
  } finally {
    deleting.value = false
  }
}

// 监听评分变化，更新主题
watch(
  () => form.rating,
  (newRating) => {
    themeStore.setMoodByRating(newRating)
  }
)

onMounted(() => {
  // 检查日记是否存在
  if (!diary.value) {
    message.error('日记不存在')
    router.push('/')
    return
  }
  
  // 初始化表单
  initForm()
})

// 监听日记变化
watch(diary, (newDiary) => {
  if (newDiary) {
    initForm()
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.edit-diary-page {
  min-height: 100vh;
  
  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-2xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-lg);
      text-align: center;
    }
    
    .header-left {
      .back-btn {
        :deep(.n-button__content) {
          gap: var(--spacing-sm);
        }
      }
    }
    
    .header-center {
      .page-title {
        font-size: var(--text-3xl);
        font-weight: var(--font-bold);
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
        background: var(--gradient-bg);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
      
      .page-subtitle {
        color: var(--text-secondary);
        font-size: var(--text-lg);
      }
    }
    
    .header-right {
      @media (max-width: 768px) {
        display: none;
      }
    }
  }
  
  .loading-state,
  .error-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    
    .loading-content,
    .error-content {
      text-align: center;
      
      p, h3 {
        margin: var(--spacing-md) 0;
        color: var(--text-secondary);
      }
      
      h3 {
        color: var(--text-primary);
        font-size: var(--text-xl);
      }
    }
  }
  
  .diary-form {
    max-width: 800px;
    margin: 0 auto;
    
    .date-display {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      padding: var(--spacing-md);
      background: var(--gray-50);
      border-radius: var(--radius);
      color: var(--text-secondary);
      font-weight: var(--font-medium);
    }
    
    .rating-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: var(--spacing-lg);
      
      @media (max-width: 640px) {
        flex-direction: column;
        gap: var(--spacing-md);
      }
      
      .rating-input {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
        
        :deep(.n-rate) {
          font-size: 24px;
        }
        
        .rating-text {
          font-size: var(--text-lg);
          font-weight: var(--font-medium);
          color: var(--primary-color);
        }
      }
      
      .rating-preview {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: var(--spacing-md);
        background: var(--gray-50);
        border-radius: var(--radius-lg);
        border: 2px dashed var(--border-color);
        transition: all var(--duration-300) var(--ease-out);
        
        &:hover {
          border-color: var(--primary-color);
          background: var(--primary-color-lighter);
        }
      }
    }
    
    .content-input {
      :deep(.n-input__textarea) {
        font-size: var(--text-base);
        line-height: var(--leading-relaxed);
        border-radius: var(--radius-lg);
        
        &:focus {
          border-color: var(--primary-color);
          box-shadow: 0 0 0 2px var(--primary-color)20;
        }
      }
    }
    
    .form-actions {
      display: flex;
      gap: var(--spacing-md);
      justify-content: flex-end;
      margin-top: var(--spacing-xl);
      
      @media (max-width: 640px) {
        flex-direction: column-reverse;
      }
      
      .cancel-btn,
      .delete-btn,
      .submit-btn {
        min-width: 120px;
        
        :deep(.n-button__content) {
          gap: var(--spacing-sm);
        }
      }
    }
  }
}

// 表单标签样式
:deep(.n-form-item-label) {
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  font-size: var(--text-base);
}

// 表单项间距
:deep(.n-form-item) {
  margin-bottom: var(--spacing-xl);
}
</style> 