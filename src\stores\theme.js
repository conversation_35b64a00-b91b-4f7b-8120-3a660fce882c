import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'

export const useThemeStore = defineStore('theme', () => {
  // 深浅色模式
  const isDark = ref(false)
  
  // 当前心情主色调
  const currentMoodColor = ref('neutral')
  
  // 心情色彩映射
  const moodColors = {
    happy: {
      primary: '#FF6B35',
      secondary: '#FFB84D',
      gradient: 'linear-gradient(135deg, #FF6B35 0%, #FFB84D 100%)',
      rgb: '255, 107, 53'
    },
    calm: {
      primary: '#4ECDC4',
      secondary: '#44A08D',
      gradient: 'linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%)',
      rgb: '78, 205, 196'
    },
    sad: {
      primary: '#6C7B95',
      secondary: '#9B59B6',
      gradient: 'linear-gradient(135deg, #6C7B95 0%, #9B59B6 100%)',
      rgb: '108, 123, 149'
    },
    neutral: {
      primary: '#95A5A6',
      secondary: '#7F8C8D',
      gradient: 'linear-gradient(135deg, #95A5A6 0%, #7F8C8D 100%)',
      rgb: '149, 165, 166'
    }
  }
  
  // 计算当前主题色
  const currentTheme = computed(() => moodColors[currentMoodColor.value])
  
  // 根据评分设置心情色调
  const setMoodByRating = (rating) => {
    if (rating >= 4) {
      currentMoodColor.value = 'happy'
    } else if (rating === 3) {
      currentMoodColor.value = 'calm'
    } else if (rating <= 2) {
      currentMoodColor.value = 'sad'
    } else {
      currentMoodColor.value = 'neutral'
    }
  }
  
  // 切换深浅色模式
  const toggleDark = () => {
    isDark.value = !isDark.value
    localStorage.setItem('mood-diary-dark', isDark.value.toString())
    updateCSSVariables()
  }
  
  // 从localStorage初始化主题
  const initTheme = () => {
    const savedDark = localStorage.getItem('mood-diary-dark')
    if (savedDark !== null) {
      isDark.value = savedDark === 'true'
    }
    
    const savedMoodColor = localStorage.getItem('mood-diary-mood-color')
    if (savedMoodColor && moodColors[savedMoodColor]) {
      currentMoodColor.value = savedMoodColor
    }
    
    // 立即应用主题
    updateCSSVariables()
  }
  
  // 监听主色调变化并保存
  watch(currentMoodColor, (newColor) => {
    localStorage.setItem('mood-diary-mood-color', newColor)
    updateCSSVariables()
  })
  
  // 监听深浅色模式变化并更新CSS变量
  watch(isDark, () => {
    updateCSSVariables()
  })
  
  // 更新CSS变量
  const updateCSSVariables = () => {
    const root = document.documentElement
    const theme = currentTheme.value
    
    root.style.setProperty('--primary-color', theme.primary)
    root.style.setProperty('--secondary-color', theme.secondary)
    root.style.setProperty('--gradient-bg', theme.gradient)
    root.style.setProperty('--primary-color-rgb', theme.rgb)
    
    // 背景渐变 - 浅色主题
    const lightBg = `linear-gradient(135deg, ${theme.primary}12 0%, ${theme.secondary}12 100%)`
    // 背景渐变 - 深色主题
    const darkBg = `linear-gradient(135deg, ${theme.primary}20 0%, ${theme.secondary}20 100%)`
    
    root.style.setProperty('--bg-gradient-light', lightBg)
    root.style.setProperty('--bg-gradient-dark', darkBg)
    
    // 根据当前主题设置额外的颜色变量
    if (isDark.value) {
      // 深色主题下的主色调适配（透明度更高以适应深色背景）
      root.style.setProperty('--primary-color-light', theme.primary + '40')
      root.style.setProperty('--primary-color-lighter', theme.primary + '25')
    } else {
      // 浅色主题下的主色调适配（透明度较低以适应浅色背景）
      root.style.setProperty('--primary-color-light', theme.primary + '20')
      root.style.setProperty('--primary-color-lighter', theme.primary + '10')
    }
    
    // 强制更新文档类名以确保样式生效
    if (isDark.value) {
      document.body.classList.add('dark-theme')
      document.body.classList.remove('light-theme')
    } else {
      document.body.classList.add('light-theme')
      document.body.classList.remove('dark-theme')
    }
  }
  
  return {
    isDark,
    currentMoodColor,
    currentTheme,
    moodColors,
    setMoodByRating,
    toggleDark,
    initTheme,
    updateCSSVariables
  }
}) 