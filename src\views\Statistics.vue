<template>
  <div class="statistics-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <h1 class="page-title">情绪统计</h1>
          <p class="page-subtitle">了解你的心情变化轨迹</p>
        </div>
        
        <div class="header-animation" v-if="diaryStore.diaries.length > 0">
          <MoodAnimation :rating="getCurrentMoodRating()" :size="80" />
        </div>
      </div>
      
      <!-- 统计概览 -->
      <div class="stats-overview" v-if="diaryStore.diaries.length > 0">
        <div class="overview-grid">
          <div class="stat-card">
            <div class="stat-icon">
              <n-icon size="24" :color="themeStore.currentTheme.primary">
                <DocumentTextOutline />
              </n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ diaryStore.diaries.length }}</div>
              <div class="stat-label">总日记数</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <n-icon size="24" :color="themeStore.currentTheme.primary">
                <HeartOutline />
              </n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ diaryStore.averageRating }}★</div>
              <div class="stat-label">平均心情</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <n-icon size="24" :color="themeStore.currentTheme.primary">
                <CalendarOutline />
              </n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ getActiveDays() }}</div>
              <div class="stat-label">记录天数</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <n-icon size="24" :color="themeStore.currentTheme.primary">
                <TrendingUpOutline />
              </n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ getMoodTrend() }}</div>
              <div class="stat-label">情绪趋势</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 图表区域 -->
      <div class="charts-section" v-if="diaryStore.diaries.length > 0">
        <div class="charts-grid">
          <!-- 趋势图 -->
          <div class="chart-card">
            <EmotionTrendChart :data="diaryStore.recentTrend" />
          </div>
          
          <!-- 情绪分布 -->
          <div class="chart-card">
            <div class="emotion-distribution">
              <div class="chart-header">
                <h3 class="chart-title">情绪分布</h3>
              </div>
              
              <div class="distribution-list">
                <div 
                  v-for="(count, rating) in diaryStore.emotionStats" 
                  :key="rating"
                  class="distribution-item"
                  :class="{ active: count > 0 }"
                >
                  <div class="item-left">
                    <MoodAnimation :rating="parseInt(rating)" :size="40" />
                    <div class="item-info">
                      <div class="item-rating">{{ rating }}★</div>
                      <div class="item-text">{{ getRatingText(parseInt(rating)) }}</div>
                    </div>
                  </div>
                  
                  <div class="item-right">
                    <div class="count-bar">
                      <div 
                        class="bar" 
                        :style="{ 
                          width: getPercentage(count) + '%',
                          background: getRatingColor(parseInt(rating))
                        }"
                      ></div>
                    </div>
                    <div class="count-text">{{ count }}次</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 最近记录 -->
      <div class="recent-entries" v-if="diaryStore.diaries.length > 0">
        <div class="section-header">
          <h2 class="section-title">最近记录</h2>
          <n-button 
            text 
            type="primary" 
            @click="$router.push('/')"
            class="view-all-btn"
          >
            查看全部
            <template #icon>
              <n-icon>
                <ArrowForwardOutline />
              </n-icon>
            </template>
          </n-button>
        </div>
        
        <div class="entries-list">
          <DiaryCard
            v-for="diary in recentDiaries"
            :key="diary.id"
            :diary="diary"
            @edit="handleEdit"
            @delete="handleDelete"
          />
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="diaryStore.diaries.length === 0" class="empty-state">
        <div class="empty-content">
          <MoodAnimation :rating="3" :size="120" />
          <h3 class="empty-title">暂无统计数据</h3>
          <p class="empty-description">开始写日记，记录你的心情变化吧</p>
          <n-button 
            type="primary" 
            size="large"
            @click="$router.push('/add')"
            class="empty-action"
          >
            <template #icon>
              <n-icon>
                <AddOutline />
              </n-icon>
            </template>
            写第一篇日记
          </n-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { useDiaryStore } from '@/stores/diary'
import { useThemeStore } from '@/stores/theme'
import EmotionTrendChart from '@/components/Charts/EmotionTrendChart.vue'
import DiaryCard from '@/components/DiaryCard.vue'
import MoodAnimation from '@/components/MoodAnimation.vue'
import { getMoodText, getMoodColor } from '@/constants/mood'
import dayjs from 'dayjs'
import {
  DocumentTextOutline,
  HeartOutline,
  CalendarOutline,
  TrendingUpOutline,
  ArrowForwardOutline,
  AddOutline
} from '@vicons/ionicons5'

const router = useRouter()
const message = useMessage()
const diaryStore = useDiaryStore()
const themeStore = useThemeStore()

// 最近的日记（显示3条）
const recentDiaries = computed(() => {
  return diaryStore.diaries.slice(0, 3)
})

// 获取当前心情评分
const getCurrentMoodRating = () => {
  if (diaryStore.diaries.length === 0) return 3
  return diaryStore.diaries[0].rating
}

// 获取活跃天数
const getActiveDays = () => {
  const uniqueDates = new Set(diaryStore.diaries.map(d => d.date))
  return uniqueDates.size
}

// 获取情绪趋势
const getMoodTrend = () => {
  if (diaryStore.diaries.length < 2) return '无趋势'
  
  const recent = diaryStore.recentTrend.filter(d => d.rating > 0)
  if (recent.length < 2) return '无趋势'
  
  const lastRating = recent[recent.length - 1].rating
  const prevRating = recent[recent.length - 2].rating
  
  if (lastRating > prevRating) return '上升'
  if (lastRating < prevRating) return '下降'
  return '平稳'
}

// 获取评分文字（使用常量）
const getRatingText = (rating) => getMoodText(rating)

// 获取评分颜色（使用常量）
const getRatingColor = (rating) => getMoodColor(rating)

// 计算百分比
const getPercentage = (count) => {
  const total = diaryStore.diaries.length
  return total > 0 ? (count / total) * 100 : 0
}

// 编辑日记
const handleEdit = (diary) => {
  router.push(`/edit/${diary.id}`)
}

// 删除日记
const handleDelete = (id) => {
  const success = diaryStore.deleteDiary(id)
  if (success) {
    message.success('日记已删除')
  } else {
    message.error('删除失败')
  }
}
</script>

<style lang="scss" scoped>
.statistics-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-2xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-lg);
      text-align: center;
    }
    
    .header-content {
      .page-title {
        font-size: var(--text-3xl);
        font-weight: var(--font-bold);
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
        background: var(--gradient-bg);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
      
      .page-subtitle {
        color: var(--text-secondary);
        font-size: var(--text-lg);
      }
    }
    
    .header-animation {
      @media (max-width: 768px) {
        display: none;
      }
    }
  }
  
  .stats-overview {
    margin-bottom: var(--spacing-2xl);
    
    .overview-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-lg);
      
      .stat-card {
        @include card-style;
        padding: var(--spacing-lg);
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        transition: all var(--duration-300) var(--ease-out);
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: var(--shadow-lg);
        }
        
        .stat-icon {
          padding: var(--spacing-sm);
          background: var(--primary-color-light);
          border-radius: var(--radius);
        }
        
        .stat-content {
          .stat-value {
            font-size: var(--text-2xl);
            font-weight: var(--font-bold);
            color: var(--primary-color);
            line-height: var(--leading-tight);
          }
          
          .stat-label {
            font-size: var(--text-sm);
            color: var(--text-tertiary);
            margin-top: var(--spacing-xs);
          }
        }
      }
    }
  }
  
  .charts-section {
    margin-bottom: var(--spacing-2xl);
    
    .charts-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: var(--spacing-lg);
      
      @include respond-to(lg) {
        grid-template-columns: 2fr 1fr;
      }
      
      .chart-card {
        .emotion-distribution {
          @include card-style;
          padding: var(--spacing-lg);
          
          .chart-header {
            margin-bottom: var(--spacing-lg);
            
            .chart-title {
              font-size: var(--text-lg);
              font-weight: var(--font-semibold);
              color: var(--text-primary);
            }
          }
          
          .distribution-list {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
            
            .distribution-item {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: var(--spacing-sm);
              border-radius: var(--radius);
              transition: all var(--duration-200) var(--ease-out);
              
              &.active {
                background: var(--gray-50);
              }
              
              .item-left {
                display: flex;
                align-items: center;
                gap: var(--spacing-sm);
                
                .item-info {
                  .item-rating {
                    font-weight: var(--font-semibold);
                    color: var(--text-primary);
                    font-size: var(--text-sm);
                  }
                  
                  .item-text {
                    font-size: var(--text-xs);
                    color: var(--text-tertiary);
                  }
                }
              }
              
              .item-right {
                display: flex;
                align-items: center;
                gap: var(--spacing-sm);
                
                .count-bar {
                  width: 60px;
                  height: 6px;
                  background: var(--gray-200);
                  border-radius: var(--radius-full);
                  overflow: hidden;
                  
                  .bar {
                    height: 100%;
                    border-radius: var(--radius-full);
                    transition: width var(--duration-500) var(--ease-out);
                  }
                }
                
                .count-text {
                  font-size: var(--text-sm);
                  color: var(--text-secondary);
                  font-weight: var(--font-medium);
                  min-width: 30px;
                  text-align: right;
                }
              }
            }
          }
        }
      }
    }
  }
  
  .recent-entries {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-lg);
      
      .section-title {
        font-size: var(--text-xl);
        font-weight: var(--font-semibold);
        color: var(--text-primary);
      }
      
      .view-all-btn {
        :deep(.n-button__content) {
          gap: var(--spacing-xs);
        }
      }
    }
    
    .entries-list {
      display: grid;
      gap: var(--spacing-lg);
    }
  }
  
  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 500px;
    
    .empty-content {
      text-align: center;
      max-width: 400px;
      
      .empty-title {
        font-size: var(--text-xl);
        font-weight: var(--font-semibold);
        color: var(--text-primary);
        margin: var(--spacing-lg) 0 var(--spacing-sm);
      }
      
      .empty-description {
        color: var(--text-secondary);
        margin-bottom: var(--spacing-lg);
        line-height: var(--leading-relaxed);
      }
      
      .empty-action {
        :deep(.n-button__content) {
          gap: var(--spacing-sm);
        }
      }
    }
  }
}
</style> 