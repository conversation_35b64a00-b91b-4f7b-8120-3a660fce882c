<template>
  <div class="music-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <h1 class="page-title">音乐搜索</h1>
          <p class="page-subtitle">为你的心情找到最合适的音乐</p>
        </div>
      </div>
      
      <!-- 搜索区域 -->
      <div class="search-section">
        <div class="search-box">
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索歌曲、歌手或专辑..."
            size="large"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <n-icon>
                <SearchOutline />
              </n-icon>
            </template>
          </n-input>
        </div>
        
        <!-- 热门搜索 -->
        <div class="hot-searches" v-if="!searchKeyword && hotSearches.length > 0">
          <div class="hot-title">热门搜索</div>
          <div class="hot-tags">
            <n-tag
              v-for="tag in hotSearches"
              :key="tag"
              @click="searchKeyword = tag; handleSearch()"
              class="hot-tag"
              :bordered="false"
            >
              {{ tag }}
            </n-tag>
          </div>
        </div>
      </div>
      
      <!-- 搜索结果 -->
      <div class="search-results" v-if="searchResults.length > 0">
        <div class="results-header">
          <div class="header-left">
            <h3>搜索结果</h3>
          </div>
          <div class="header-right">
            <span class="results-count">共找到 {{ searchResults.length }}+ 首歌曲</span>
          </div>
        </div>

        <div class="music-list" ref="musicListRef">
          <div
            v-for="(song, index) in searchResults"
            :key="song.id"
            class="music-item"
            :class="{
              active: musicStore.currentSong && musicStore.currentSong.id === song.id,
              playing: musicStore.currentSong && musicStore.currentSong.id === song.id && musicStore.isPlaying
            }"
          >
            <div class="item-index">
              <span v-if="!isCurrentSong(song) || !musicStore.isPlaying">{{ index + 1 }}</span>
              <n-icon v-else class="playing-icon">
                <VolumeHighOutline />
              </n-icon>
            </div>

            <div class="item-cover">
              <img :src="song.cover" :alt="song.name" @error="handleImageError" />
              <div class="cover-overlay" @click="handlePlay(song)">
                <n-icon size="24">
                  <PlayOutline v-if="!isCurrentSong(song) || !musicStore.isPlaying" />
                  <PauseOutline v-else />
                </n-icon>
              </div>
            </div>

            <div class="item-info">
              <div class="song-name">{{ song.name }}</div>
              <div class="song-artist">{{ song.artist }}</div>
            </div>

            <div class="item-album">{{ song.album }}</div>

            <div class="item-duration">{{ formatDuration(song.time) }}</div>

            <div class="item-actions">
              <n-button
                circle
                quaternary
                @click="toggleFavorite(song)"
                :type="musicStore.isFavorite(song.id) ? 'error' : 'default'"
              >
                <template #icon>
                  <n-icon>
                    <HeartOutline v-if="!musicStore.isFavorite(song.id)" />
                    <Heart v-else />
                  </n-icon>
                </template>
              </n-button>
            </div>
          </div>

        </div>

        <!-- 加载更多 -->
        <div class="load-more-section" v-if="searchResults.length > 0">
          <div class="load-more" v-if="musicStore.searchHasMore">
            <div
              v-if="!musicStore.searchLoadingMore"
              @click="loadMore"
              class="load-more-btn"
            >
              加载更多
            </div>
            <div v-else class="loading-more">
              <n-spin size="small" />
              <span>加载中...</span>
            </div>
          </div>

          <!-- 没有更多了 -->
          <div class="no-more" v-else>
            <span>~ 没有更多歌曲了 ~</span>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div class="empty-state" v-if="!musicStore.searchLoading && searchResults.length === 0 && searchKeyword">
        <div class="empty-icon">
          <n-icon size="64" color="#d1d5db">
            <MusicalNotesOutline />
          </n-icon>
        </div>
        <div class="empty-text">
          <h3>没有找到相关音乐</h3>
          <p>试试其他关键词吧</p>
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div class="loading-state" v-if="musicStore.searchLoading">
        <n-spin size="large">
          <template #description>
            正在搜索音乐...
          </template>
        </n-spin>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useMessage } from 'naive-ui'
import { useMusicStore } from '@/stores/music'
import { formatMusicData, formatDuration } from '@/api/music'
import {
  SearchOutline,
  PlayOutline,
  PauseOutline,
  HeartOutline,
  Heart,
  VolumeHighOutline,
  MusicalNotesOutline
} from '@vicons/ionicons5'

const message = useMessage()
const musicStore = useMusicStore()

// 搜索关键词
const searchKeyword = ref('')

// 热门搜索
const hotSearches = ref([
  '勾指起誓', '夜曲', '告白气球', '稻香', '青花瓷',
  '演员', '体面', '小幸运', '匆匆那年', '后来'
])

// 搜索结果
const searchResults = computed(() => {
  return musicStore.searchResults.map(formatMusicData).filter(Boolean)
})

// 当前搜索请求的控制器
let currentSearchController = null
// 最后一次搜索的关键词，用于避免重复搜索
let lastSearchKeyword = ''

// 搜索音乐
const handleSearch = async () => {
  const keyword = searchKeyword.value.trim()

  // 如果关键词为空，清空结果
  if (!keyword) {
    musicStore.searchResults = []
    lastSearchKeyword = ''
    return
  }

  // 如果关键词长度小于2个字符，不进行搜索
  if (keyword.length < 2) {
    message.warning('请输入至少2个字符进行搜索')
    return
  }

  // 如果关键词与上次相同，避免重复搜索
  if (keyword === lastSearchKeyword && musicStore.searchResults.length > 0) {
    return
  }

  // 取消之前的搜索请求
  if (currentSearchController && !currentSearchController.signal.aborted) {
    try {
      currentSearchController.abort()
    } catch (e) {
      // 忽略abort错误
    }
  }

  // 创建新的请求控制器
  currentSearchController = new AbortController()
  lastSearchKeyword = keyword

  try {
    // 不传递signal，让store内部处理超时
    const result = await musicStore.searchMusic(keyword, 1, 10)

    if (result && result.success) {
      if (result.data.length === 0) {
        message.info('没有找到相关音乐')
      }
    } else if (result) {
      message.error(result.message)
    }
  } catch (error) {
    // 如果是请求被取消，不显示错误信息
    if (error.name === 'AbortError') {
      return
    }
    console.error('搜索失败:', error)
    message.error('搜索失败，请稍后重试')
  } finally {
    currentSearchController = null
  }
}



// 加载更多
const loadMore = async () => {
  try {
    const result = await musicStore.loadMoreSearchResults()
    if (result && !result.success) {
      message.error(result.message)
    }
  } catch (error) {
    console.error('加载更多失败:', error)
    message.error('加载更多失败，请稍后重试')
  }
}

// 播放音乐
const handlePlay = (song) => {
  // 设置播放列表为当前搜索结果
  musicStore.setPlaylist(searchResults.value, song)
  musicStore.playSong(song)
}

// 检查是否为当前歌曲
const isCurrentSong = (song) => {
  return musicStore.currentSong && musicStore.currentSong.id === song.id
}

// 切换收藏状态
const toggleFavorite = (song) => {
  if (musicStore.isFavorite(song.id)) {
    musicStore.removeFromFavorites(song.id)
    message.success('已取消收藏')
  } else {
    musicStore.addToFavorites(song)
    message.success('已添加到收藏')
  }
}



// 处理图片加载错误
const handleImageError = (e) => {
  e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAzMkMyNCAyNy41ODE3IDI3LjU4MTcgMjQgMzIgMjRDMzYuNDE4MyAyNCA0MCAyNy41ODE3IDQwIDMyQzQwIDM2LjQxODMgMzYuNDE4MyA0MCAzMiA0MEMyNy41ODE3IDQwIDI0IDM2LjQxODMgMjQgMzJaIiBmaWxsPSIjOUNBM0FGIi8+Cjwvc3ZnPgo='
}

onMounted(() => {
  // 初始化音乐store
  musicStore.init()
})

// 组件卸载时清理资源
onUnmounted(() => {
  // 清除搜索定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
    searchTimer = null
  }

  // 取消当前搜索请求
  if (currentSearchController) {
    currentSearchController.abort()
    currentSearchController = null
  }
})
</script>

<style lang="scss" scoped>
.music-page {
  min-height: 100vh;
  padding: var(--spacing-xl) 0;
  
  .page-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    
    .page-title {
      font-size: var(--text-3xl);
      font-weight: var(--font-bold);
      color: var(--text-primary);
      margin-bottom: var(--spacing-sm);
      background: var(--gradient-bg);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .page-subtitle {
      color: var(--text-secondary);
      font-size: var(--text-lg);
    }
  }
  
  .search-section {
    margin-bottom: var(--spacing-2xl);
    
    .search-box {
      max-width: 600px;
      margin: 0 auto var(--spacing-lg);
    }
    
    .hot-searches {
      text-align: center;
      
      .hot-title {
        color: var(--text-secondary);
        margin-bottom: var(--spacing-md);
        font-size: var(--text-sm);
      }
      
      .hot-tags {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: var(--spacing-sm);
        
        .hot-tag {
          cursor: pointer;
          transition: all var(--duration-200) var(--ease-out);
          
          &:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
          }
        }
      }
    }
  }
  
  .search-results {
    .results-header {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: var(--spacing-lg);
      padding: var(--spacing-lg);
      background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.05), rgba(var(--primary-color-rgb), 0.02));
      border-radius: var(--radius-lg);
      border: 1px solid rgba(var(--primary-color-rgb), 0.1);
      min-height: 80px;

      .header-left {
        flex: 1;
        display: flex;
        justify-content: center;

        h3 {
          color: var(--text-primary);
          font-size: var(--text-xl);
          font-weight: var(--font-semibold);
          margin: 0;
          text-align: center;
        }
      }

      .header-right {
        flex: 1;
        display: flex;
        justify-content: center;

        .results-count {
          color: var(--text-secondary);
          font-size: var(--text-sm);
          font-weight: var(--font-medium);
          background: rgba(var(--text-secondary-rgb), 0.1);
          padding: 8px 16px;
          border-radius: var(--radius-md);
          border: 1px solid rgba(var(--text-secondary-rgb), 0.2);
          text-align: center;
        }
      }
    }
    
    .music-list {
      .music-item {
        display: grid;
        grid-template-columns: 40px 60px 1fr 200px 80px 100px;
        gap: var(--spacing-md);
        align-items: center;
        padding: var(--spacing-md);
        border-radius: var(--radius-lg);
        transition: all var(--duration-200) var(--ease-out);
        border: 1px solid transparent;
        
        &:hover {
          background: var(--gray-50);
          border-color: var(--border-color);
        }
        
        &.active {
          background: var(--primary-color-lighter);
          border-color: var(--primary-color);
        }
        
        &.playing {
          .item-index .playing-icon {
            color: var(--primary-color);
            animation: pulse 1.5s infinite;
          }
        }
        
        .item-index {
          text-align: center;
          color: var(--text-tertiary);
          font-size: var(--text-sm);
          
          .playing-icon {
            font-size: 16px;
          }
        }
        
        .item-cover {
          position: relative;
          width: 50px;
          height: 50px;
          border-radius: var(--radius);
          overflow: hidden;
          cursor: pointer;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          
          .cover-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity var(--duration-200) var(--ease-out);
            color: white;
          }
          
          &:hover .cover-overlay {
            opacity: 1;
          }
        }
        
        .item-info {
          min-width: 0;
          
          .song-name {
            font-weight: var(--font-medium);
            color: var(--text-primary);
            margin-bottom: 2px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          
          .song-artist {
            color: var(--text-secondary);
            font-size: var(--text-sm);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        
        .item-album {
          color: var(--text-tertiary);
          font-size: var(--text-sm);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .item-duration {
          color: var(--text-tertiary);
          font-size: var(--text-sm);
          text-align: center;
        }
        
        .item-actions {
          display: flex;
          gap: var(--spacing-xs);
          justify-content: flex-end;
        }


      }
    }
  }

  .empty-state,
  .loading-state {
    text-align: center;
    padding: var(--spacing-3xl) var(--spacing-lg);

    .empty-text {
      h3 {
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
      }

      p {
        color: var(--text-tertiary);
      }
    }
  }

  .load-more-section {
    padding: var(--spacing-xl) 0;
    text-align: center;

    .load-more {
      .load-more-btn {
        display: inline-block;
        padding: var(--spacing-md) var(--spacing-xl);
        background: transparent;
        color: var(--primary-color);
        border: 2px solid var(--primary-color);
        border-radius: var(--radius-full);
        cursor: pointer;
        transition: all var(--duration-200) var(--ease-out);
        font-weight: var(--font-medium);
        font-size: var(--text-base);

        &:hover {
          background: var(--primary-color);
          color: var(--white);
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(var(--primary-color-rgb), 0.3);
        }

        &:active {
          transform: translateY(0);
        }
      }

      .loading-more {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        color: var(--text-secondary);
        padding: var(--spacing-md);
        font-size: var(--text-base);
      }
    }

    .no-more {
      padding: var(--spacing-lg);
      color: var(--text-tertiary);
      font-size: var(--text-sm);
      font-style: italic;
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@media (max-width: 768px) {
  .music-page {
    padding: var(--spacing-lg) 0;

    .page-header {
      margin-bottom: var(--spacing-lg);

      .page-title {
        font-size: var(--text-2xl);
      }

      .page-subtitle {
        font-size: var(--text-base);
      }
    }

    .search-section {
      margin-bottom: var(--spacing-lg);

      .search-box {
        margin-bottom: var(--spacing-md);
      }

      .hot-searches {
        .hot-tags {
          gap: var(--spacing-xs);
        }
      }
    }

    .search-results {
      .results-header {
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
        min-height: 100px;

        .header-left {
          flex: none;

          h3 {
            font-size: var(--text-lg);
            text-align: center;
          }
        }

        .header-right {
          flex: none;

          .results-count {
            font-size: var(--text-sm);
            padding: 6px 12px;
          }
        }
      }

      .music-list {
        .music-item {
          grid-template-columns: 30px 50px 1fr 50px 40px;
          gap: var(--spacing-sm);
          padding: var(--spacing-sm);

          .item-album {
            display: none;
          }

          .item-duration {
            font-size: var(--text-xs);
            text-align: right;
          }

          .item-actions {
            display: flex;
            justify-content: center;
            align-items: center;

            .n-button {
              --n-height: 32px;
              --n-width: 32px;
            }
          }

          .item-info {
            .song-name {
              font-size: var(--text-sm);
              margin-bottom: 1px;
            }

            .song-artist {
              font-size: var(--text-xs);
            }
          }
        }


      }
    }

    .empty-state,
    .loading-state {
      padding: var(--spacing-2xl) var(--spacing-md);
    }

    .load-more-section {
      padding: var(--spacing-md) 0;

      .load-more {
        .load-more-btn {
          padding: var(--spacing-sm) var(--spacing-lg);
          font-size: var(--text-sm);
        }
      }

      .no-more {
        padding: var(--spacing-md);
        font-size: var(--text-xs);
      }
    }
  }
}


</style>
