import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: { title: '我的日记' }
  },
  {
    path: '/add',
    name: 'AddDiary',
    component: () => import('@/views/AddDiary.vue'),
    meta: { title: '写日记' }
  },
  {
    path: '/edit/:id',
    name: 'EditDiary',
    component: () => import('@/views/EditDiary.vue'),
    meta: { title: '编辑日记' }
  },
  {
    path: '/statistics',
    name: 'Statistics',
    component: () => import('@/views/Statistics.vue'),
    meta: { title: '情绪统计' }
  },
  {
    path: '/music',
    name: 'Music',
    component: () => import('@/views/Music.vue'),
    meta: { title: '音乐搜索' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

router.beforeEach((to) => {
  document.title = `${to.meta.title} - 心情日记`
})

export default router 