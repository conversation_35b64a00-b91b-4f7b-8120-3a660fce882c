<template>
  <div class="app-layout">
    <!-- 顶部导航 -->
    <AppHeader />
    
    <!-- 主内容区 -->
    <main class="main-content">
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </main>
    
    <!-- 底部导航 -->
    <AppFooter />

    <!-- 音乐播放器 -->
    <MusicPlayer />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useThemeStore } from '@/stores/theme'
import { useDiaryStore } from '@/stores/diary'
import { useMusicStore } from '@/stores/music'
import AppHeader from './AppHeader.vue'
import AppFooter from './AppFooter.vue'
import MusicPlayer from '@/components/MusicPlayer.vue'

const themeStore = useThemeStore()
const diaryStore = useDiaryStore()
const musicStore = useMusicStore()

onMounted(() => {
  // 初始化主题
  themeStore.initTheme()
  themeStore.updateCSSVariables()
  
  // 加载日记数据
  diaryStore.loadDiaries()

  // 初始化音乐store
  musicStore.init()

  // 确保主题变化时更新整个界面
  setTimeout(() => {
    themeStore.updateCSSVariables()
  }, 100)
})
</script>

<style lang="scss" scoped>
.app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: var(--spacing-lg) 0;

  @include respond-to(md) {
    padding: var(--spacing-xl) 0;
  }
}
</style> 