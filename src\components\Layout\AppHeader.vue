<template>
  <header class="app-header">
    <div class="container">
      <div class="header-content">
        <!-- Logo和标题 -->
        <div class="logo-section">
          <router-link to="/" class="logo">
            <div class="logo-icon">
              <n-icon size="25" :color="themeStore.currentTheme.primary">
                <HeartOutline />
              </n-icon>
            </div>
            <h1 class="logo-text">心情日记</h1>
          </router-link>
        </div>
        
        <!-- 导航菜单 -->
        <nav class="nav-menu">
          <router-link 
            v-for="item in navItems" 
            :key="item.path"
            :to="item.path" 
            class="nav-item"
            :class="{ active: $route.path === item.path }"
          >
            <n-icon size="20">
              <component :is="item.icon" />
            </n-icon>
            <span class="nav-text">{{ item.name }}</span>
          </router-link>
        </nav>
        
        <!-- 工具栏 -->
        <div class="toolbar">
          <!-- 主题切换 -->
          <n-button 
            circle 
            quaternary 
            @click="themeStore.toggleDark"
            class="theme-toggle"
          >
            <template #icon>
              <n-icon size="20">
                <MoonOutline v-if="!themeStore.isDark" />
                <SunnyOutline v-else />
              </n-icon>
            </template>
          </n-button>
          
          <!-- 添加日记按钮 -->
          <n-button 
            type="primary" 
            @click="$router.push('/add')"
            class="add-btn"
          >
            <template #icon>
              <n-icon>
                <AddOutline />
              </n-icon>
            </template>
            <span class="add-text">写日记</span>
          </n-button>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
import { useThemeStore } from '@/stores/theme'
import {
  HeartOutline,
  HomeOutline,
  StatsChartOutline,
  MoonOutline,
  SunnyOutline,
  AddOutline
} from '@vicons/ionicons5'

const themeStore = useThemeStore()

const navItems = [
  {
    name: '我的日记',
    path: '/',
    icon: HomeOutline
  },
  {
    name: '情绪统计',
    path: '/statistics',
    icon: StatsChartOutline
  }
]
</script>

<style lang="scss" scoped>
.app-header {
  background: var(--white);
  border-bottom: 1px solid var(--border-color);
  backdrop-filter: blur(12px);
  position: sticky;
  top: 0;
  z-index: 100;
  transition: all var(--duration-300) var(--ease-out);
  
  // 深色模式适配
  .dark-theme & {
    background: rgba(var(--white), 0.9);
    backdrop-filter: blur(12px) saturate(180%);
  }
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) 0;
    gap: var(--spacing-lg);
    min-height: 64px; // 确保有足够的高度
  }
  
  .logo-section {
    .logo {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      text-decoration: none;
      color: var(--text-primary);
      transition: all var(--duration-200) var(--ease-out);

      &:hover {
        transform: translateY(-1px);

        .logo-icon {
          transform: scale(1.1);
        }
      }

      .logo-icon {
        display: flex;
        align-items: center;
        transition: transform var(--duration-200) var(--ease-out);
      }
      
      .logo-text {
        font-size: var(--text-xl);
        font-weight: var(--font-bold);
        background: var(--gradient-bg);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        transition: all var(--duration-300) var(--ease-out);
        margin: 0;
        line-height: 1.2;

        @media (max-width: 640px) {
          display: none;
        }
      }
    }
  }
  
  .nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);

    .nav-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      padding: var(--spacing-sm) var(--spacing-md);
      border-radius: var(--radius);
      text-decoration: none;
      color: var(--text-secondary);
      font-weight: var(--font-medium);
      transition: all var(--duration-200) var(--ease-out);
      position: relative;
      height: 40px; // 固定高度确保一致性
      
      &:hover {
        color: var(--primary-color);
        background: var(--gray-100);
        transform: translateY(-1px);
      }
      
      &.active {
        color: var(--primary-color);
        background: var(--primary-color-lighter);
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 20px;
          height: 2px;
          background: var(--primary-color);
          border-radius: var(--radius-full);
        }
      }
      
      .nav-text {
        font-size: var(--text-sm);
        
        @media (max-width: 640px) {
          display: none;
        }
      }
    }
  }
  
  .toolbar {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    
    .theme-toggle {
      color: var(--text-secondary);
      height: 40px;
      width: 40px;

      &:hover {
        color: var(--text-primary);
        transform: scale(1.05);
      }

      :deep(.n-button__content) {
        transition: all var(--duration-200) var(--ease-out);
      }

      &:hover {
        :deep(.n-button__content) {
          transform: rotate(15deg) scale(1.1);
        }
      }
    }
    
    .add-btn {
      background: var(--primary-color);
      border: none;
      color: white;
      height: 40px;

      &:hover {
        background: var(--secondary-color);
        // transform: translateY(-0.5px);
        box-shadow: var(--shadow-lg);
      }

      .add-text {
        @media (max-width: 640px) {
          display: none;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .app-header {
    .header-content {
      padding: var(--spacing-sm) 0;
      min-height: 56px; // 移动端适中的高度
    }

    .logo-section {
      .logo {
        .logo-icon {
          :deep(.n-icon) {
            font-size: 22px; // 移动端稍微小一点
          }
        }
      }
    }

    .nav-menu {
      gap: var(--spacing-xs);

      .nav-item {
        padding: var(--spacing-xs) var(--spacing-sm);
        height: 36px; // 移动端稍微小一点

        :deep(.n-icon) {
          font-size: 18px; // 移动端图标稍微小一点
        }
      }
    }

    .toolbar {
      .theme-toggle {
        height: 36px;
        width: 36px;

        :deep(.n-icon) {
          font-size: 18px; // 移动端图标稍微小一点
        }
      }

      .add-btn {
        height: 36px;

        :deep(.n-icon) {
          font-size: 18px; // 移动端图标稍微小一点
        }
      }
    }
  }
}

// 更小屏幕的优化
@media (max-width: 480px) {
  .app-header {
    .header-content {
      min-height: 52px; // 更小屏幕更紧凑
      gap: var(--spacing-sm);
    }

    .logo-section {
      .logo {
        .logo-icon {
          :deep(.n-icon) {
            font-size: 20px; // 更小的图标
          }
        }
      }
    }

    .nav-menu {
      .nav-item {
        height: 32px; // 更小的高度
        padding: var(--spacing-xs);

        :deep(.n-icon) {
          font-size: 16px; // 更小的图标
        }
      }
    }

    .toolbar {
      gap: var(--spacing-xs);

      .theme-toggle {
        height: 32px;
        width: 32px;

        :deep(.n-icon) {
          font-size: 16px; // 更小的图标
        }
      }

      .add-btn {
        height: 32px;
        padding: 0 var(--spacing-sm);

        :deep(.n-icon) {
          font-size: 16px; // 更小的图标
        }
      }
    }
  }
}
</style> 